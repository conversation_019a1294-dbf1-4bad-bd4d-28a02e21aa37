"""change_storage_limit_to_biginteger

Revision ID: 6b3f21e5126d
Revises: 7fc3e61b6b94
Create Date: 2025-06-30 22:58:12.343378

This migration changes the storage_limit column from Integer to BigInteger
for consistency with storage_used and to support larger storage limits
(e.g., several terabytes) in future subscription plans.

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6b3f21e5126d'
down_revision: Union[str, None] = '7fc3e61b6b94'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Change storage_limit column from Integer to BigInteger."""
    print("🔧 Changing storage_limit column type from Integer to BigInteger...")

    # Change the column type from INTEGER to BIGINT
    # This is a safe operation that expands the range of possible values
    op.execute("""
        ALTER TABLE subscriptions
        ALTER COLUMN storage_limit TYPE BIGINT
    """)

    print("✅ Successfully changed storage_limit to BigInteger")


def downgrade() -> None:
    """Change storage_limit column from BigInteger back to Integer."""
    print("🔧 Changing storage_limit column type from BigInteger to Integer...")

    # WARNING: This downgrade could cause data loss if any storage_limit values
    # exceed the Integer range (2,147,483,647 bytes ≈ 2GB)
    op.execute("""
        ALTER TABLE subscriptions
        ALTER COLUMN storage_limit TYPE INTEGER
    """)

    print("✅ Successfully changed storage_limit back to Integer")
