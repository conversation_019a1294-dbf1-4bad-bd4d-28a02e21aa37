# Rayuela API - Guías de Inicio Rápido por Lenguaje

En este directorio encontrarás guías de inicio rápido específicas para diferentes lenguajes de programación.

## Lenguajes Disponibles

- [Python](./python/README.md) - Guía completa para integrar Rayuela en aplicaciones Python
- [Node.js](./nodejs/README.md) - Guía completa para integrar Rayuela en aplicaciones Node.js

## Próximamente

Estamos trabajando en guías para los siguientes lenguajes:

- PHP
- Ruby
- Java
- Go

## Estructura de las Guías

Cada guía incluye:

1. **Requisitos e instalación** - Dependencias necesarias para usar la API
2. **Registro y obtención de API Key** - Cómo registrarse y obtener tu API Key
3. **Cliente API** - Implementación de un cliente para interactuar con la API
4. **Flujo completo** - Desde la autenticación hasta obtener recomendaciones
5. **Ejemplos de código** - Ejemplos prácticos que puedes copiar y pegar

## Recursos Adicionales

- [Documentación Completa de la API](https://api.rayuela.ai/api/docs)
- [Guía General de Inicio Rápido](../QUICKSTART.md)
- [Repositorio de Ejemplos](https://github.com/rayuela-examples)
- [Soporte](mailto:<EMAIL>)
