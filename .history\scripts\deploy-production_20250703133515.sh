#!/bin/bash

# 🚀 Script de Despliegue Automatizado para Producción
# Incluye configuración de IAM y verificaciones post-despliegue

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuración de flags para controlar qué pasos ejecutar
SETUP_IAM=true
VERIFY_DEPLOYMENT=true
SKIP_INFRASTRUCTURE=false

# Procesar argumentos de línea de comandos
while [[ $# -gt 0 ]]; do
    case $1 in
        --direct)
            DIRECT_DEPLOY=true
            shift
            ;;
        --skip-iam)
            SETUP_IAM=false
            shift
            ;;
        --skip-verification)
            VERIFY_DEPLOYMENT=false
            shift
            ;;
        --skip-infrastructure)
            SKIP_INFRASTRUCTURE=true
            shift
            ;;
        --help)
            echo "Uso: $0 [opciones]"
            echo "Opciones:"
            echo "  --direct              Usar despliegue directo con Cloud Build"
            echo "  --skip-iam           Saltar configuración de IAM"
            echo "  --skip-verification  Saltar verificación post-despliegue"
            echo "  --skip-infrastructure Saltar configuración de infraestructura"
            echo "  --help               Mostrar esta ayuda"
            exit 0
            ;;
        *)
            print_error "Opción desconocida: $1"
            print_error "Usa --help para ver las opciones disponibles"
            exit 1
            ;;
    esac
done

print_status "🚀 DESPLIEGUE AUTOMATIZADO DE PRODUCCIÓN"
print_status "========================================"

# Verificar prerrequisitos básicos
print_status "🔍 Verificando prerrequisitos..."

if ! command -v gcloud &> /dev/null; then
    print_error "gcloud CLI no está instalado"
    exit 1
fi

PROJECT_ID=$(gcloud config get-value project)
if [ -z "$PROJECT_ID" ]; then
    print_error "No hay proyecto configurado. Ejecuta: gcloud config set project TU_PROJECT_ID"
    exit 1
fi

print_success "Proyecto: $PROJECT_ID"

# Verificar que el archivo de configuración existe
if [ ! -f "cloudbuild-deploy-production.yaml" ]; then
    print_error "Archivo cloudbuild-deploy-production.yaml no encontrado"
    exit 1
fi

print_success "✅ Archivo de configuración encontrado"

# Verificar rama actual y hacer commit si es necesario
CURRENT_BRANCH=$(git branch --show-current 2>/dev/null || echo "main")
print_status "Rama actual: $CURRENT_BRANCH"

if ! git diff --quiet HEAD || ! git diff --cached --quiet; then
    print_warning "Hay cambios sin commitear"
    print_status "Haciendo commit automático..."
    git add .
    git commit -m "chore: prepare for production deployment $(date '+%Y-%m-%d %H:%M:%S')"
    if git push origin $CURRENT_BRANCH; then
        print_success "Cambios commiteados y pusheados"
    else
        print_warning "Push falló, pero continuando con deployment local"
    fi
fi

# PASO 1: Configurar IAM (Nuevo)
if [ "$SETUP_IAM" = true ]; then
    print_status "🔐 PASO 1: Configurando IAM con Principio de Menor Privilegio..."
    
    if [ ! -f "scripts/security/setup-iam-service-accounts.sh" ]; then
        print_error "Script de IAM no encontrado: scripts/security/setup-iam-service-accounts.sh"
        exit 1
    fi
    
            if bash scripts/security/setup-iam-service-accounts.sh "$PROJECT_ID"; then
        print_success "✅ Configuración de IAM completada (incluye Artifact Registry)"
        
        # Validar configuración de IAM
        print_status "🔍 Validando configuración de seguridad IAM..."
        if [ -f "scripts/security/validate-iam-security.sh" ]; then
            if bash scripts/security/validate-iam-security.sh "$PROJECT_ID"; then
                print_success "✅ Validación de IAM exitosa"
            else
                print_warning "⚠️ Advertencias encontradas en validación IAM (continuando)"
            fi
        else
            print_warning "Script de validación IAM no encontrado"
        fi
    else
        print_error "❌ Error configurando IAM"
        exit 1
    fi
else
    print_warning "⏭️ Saltando configuración de IAM (--skip-iam especificado)"
fi

# PASO 2: Configurar infraestructura
if [ "$SKIP_INFRASTRUCTURE" = false ]; then
    print_status "🏗️ PASO 2: Verificando y configurando infraestructura..."
    
    if [ ! -f "scripts/setup-infrastructure.sh" ]; then
        print_error "Script de infraestructura no encontrado: scripts/setup-infrastructure.sh"
        exit 1
    fi
    
    if bash scripts/setup-infrastructure.sh; then
        print_success "✅ Infraestructura configurada correctamente"
    else
        print_error "❌ Error configurando infraestructura"
        exit 1
    fi
else
    print_warning "⏭️ Saltando configuración de infraestructura (--skip-infrastructure especificado)"
fi

# PASO 3: Ejecutar despliegue
print_status "🚀 PASO 3: Ejecutando despliegue en producción..."

if [ "${DIRECT_DEPLOY:-false}" = true ]; then
    print_status "Ejecutando Cloud Build directamente..."
    
    gcloud beta builds submit \
        --config=cloudbuild-deploy-production.yaml \
        --region=us-central1 \
        .
    
    DEPLOY_SUCCESS=$?
else
    # Lógica de trigger (código existente)
    print_status "Verificando triggers disponibles..."
    
    TRIGGERS=$(gcloud builds triggers list --filter="name~rayuela" --format="value(name)" --region=us-central1)
    
    if [ -z "$TRIGGERS" ]; then
        print_warning "No se encontraron triggers configurados"
        print_status "Creando trigger temporal..."
        
        gcloud builds triggers create github \
            --repo-name=rayuela \
            --repo-owner=marcelo-vera \
            --branch-pattern="^${CURRENT_BRANCH}$" \
            --build-config=cloudbuild-deploy-production.yaml \
            --name="rayuela-production-trigger" \
            --description="Trigger temporal para despliegue en producción" \
            --region=us-central1
        
        TRIGGER_NAME="rayuela-production-trigger"
    else
        TRIGGER_NAME=$(echo "$TRIGGERS" | head -n1)
        print_status "Usando trigger existente: $TRIGGER_NAME"
    fi
    
    BUILD_OUTPUT=$(gcloud builds triggers run $TRIGGER_NAME \
        --branch=$CURRENT_BRANCH \
        --region=us-central1 2>&1)
    
    BUILD_ID=$(echo "$BUILD_OUTPUT" | grep -o "id: [a-zA-Z0-9-]*" | cut -d' ' -f2)
    
    if [ -n "$BUILD_ID" ]; then
        print_success "Build iniciado con ID: $BUILD_ID"
        print_status "🔗 Seguir progreso: https://console.cloud.google.com/cloud-build/builds/$BUILD_ID?project=$PROJECT_ID"
        
        # Esperar a que termine el build
        print_status "Esperando a que termine el build..."
        gcloud builds log $BUILD_ID --stream --region=us-central1
        
        BUILD_STATUS=$(gcloud builds describe $BUILD_ID --format="value(status)" --region=us-central1)
        
        if [ "$BUILD_STATUS" = "SUCCESS" ]; then
            DEPLOY_SUCCESS=0
        else
            DEPLOY_SUCCESS=1
        fi
    else
        print_error "No se pudo obtener el ID del build"
        DEPLOY_SUCCESS=1
    fi
fi

# PASO 4: Verificación post-despliegue (Nuevo)
if [ $DEPLOY_SUCCESS -eq 0 ]; then
    print_success "🎉 ¡Despliegue completado exitosamente!"
    
    if [ "$VERIFY_DEPLOYMENT" = true ]; then
        print_status "🔍 PASO 4: Ejecutando verificaciones post-despliegue..."
        
        if [ -f "scripts/verify-production-deployment.sh" ]; then
            if bash scripts/verify-production-deployment.sh; then
                print_success "✅ Todas las verificaciones post-despliegue pasaron"
            else
                print_warning "⚠️ Algunas verificaciones fallaron (revisar logs)"
            fi
        else
            print_warning "Script de verificación no encontrado"
            
            # Verificaciones básicas integradas
            print_status "Ejecutando verificaciones básicas..."
            
            # Mostrar URLs de servicios
            print_status "🌐 Servicios desplegados:"
            
            BACKEND_URL=$(gcloud run services describe rayuela-backend --region=us-central1 --format="value(status.url)" 2>/dev/null || echo "No disponible")
            FRONTEND_URL=$(gcloud run services describe rayuela-frontend --region=us-central1 --format="value(status.url)" 2>/dev/null || echo "No disponible")
            
            echo "🔧 Backend API: $BACKEND_URL"
            echo "🖥️  Frontend: $FRONTEND_URL"
            
            if [ "$BACKEND_URL" != "No disponible" ]; then
                print_status "🏥 Probando backend..."
                if curl -f --max-time 10 "$BACKEND_URL/health" &>/dev/null; then
                    print_success "✅ Backend responde correctamente"
                else
                    print_warning "⚠️ Backend no responde en /health"
                fi
            fi
        fi
    else
        print_warning "⏭️ Saltando verificaciones post-despliegue (--skip-verification especificado)"
    fi
    
    # Configurar URLs dinámicamente (elimina hardcoding)
    echo ""
    print_status "🔧 Configurando URLs dinámicamente..."
    if [ -f "scripts/configure-dynamic-urls.sh" ]; then
        bash scripts/configure-dynamic-urls.sh
        print_success "✅ URLs configuradas dinámicamente"
    else
        print_warning "⚠️ Script de configuración dinámica no encontrado"
    fi

    # Mostrar resumen final
    echo ""
    print_success "📋 RESUMEN DEL DESPLIEGUE:"
    echo "✅ Configuración IAM: $([ "$SETUP_IAM" = true ] && echo "Completada" || echo "Saltada")"
    echo "✅ Infraestructura: $([ "$SKIP_INFRASTRUCTURE" = false ] && echo "Verificada" || echo "Saltada")"
    echo "✅ Despliegue: Exitoso"
    echo "✅ URLs Dinámicas: Configuradas"
    echo "✅ Verificaciones: $([ "$VERIFY_DEPLOYMENT" = true ] && echo "Ejecutadas" || echo "Saltadas")"
    
    echo ""
    print_success "📋 PRÓXIMOS PASOS RECOMENDADOS:"
    echo "1. Configurar dominio personalizado (si no está hecho)"
    echo "2. Configurar SSL/TLS certificado"
    echo "3. Configurar monitoreo (ejecutar scripts/setup-monitoring.sh)"
    echo "4. Configurar backups de base de datos"
    echo "5. Revisar logs de aplicación"
    
else
    print_error "❌ El despliegue falló"
    print_error "📋 Revisar logs y ejecutar verificaciones manuales"
    exit 1
fi

print_success "🎯 Despliegue automatizado completado exitosamente" 