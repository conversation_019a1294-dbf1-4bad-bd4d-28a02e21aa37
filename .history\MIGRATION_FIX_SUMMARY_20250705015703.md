# Resumen de Corrección de Migración - system_users

## Problema Identificado

La migración `20250704_120000_add_system_user_id_to_audit_logs.py` estaba fallando con el siguiente error:

```
sqlalchemy.exc.ProgrammingError: there is no unique constraint matching given keys for referenced table "system_users"
[SQL: ALTER TABLE audit_logs ADD CONSTRAINT fk_audit_log_system_user FOREIGN KEY(account_id, system_user_id) REFERENCES system_users (account_id, id) ON DELETE SET NULL]
```

## Causa Raíz

El problema era una inconsistencia entre la estructura de la base de datos y los modelos SQLAlchemy:

1. **Base de datos actual**:
   - La tabla `roles` tenía una primary key simple en `role_id`
   - La tabla `system_users` tenía una primary key simple en `user_id`
2. **Modelo SQLAlchemy**:
   - Esperaba `roles` con primary key compuesta `(account_id, id)`
   - Esperaba `system_users` con primary key compuesta `(account_id, id)`
3. **Migración**: Intentaba crear foreign keys compuestas que referenciaban primary keys que no existían

## Solución Implementada

### Cambios en la Migración

La migración `20250704_120000_add_system_user_id_to_audit_logs.py` fue corregida para:

1. **Reestructurar `roles` primero**:
   - Eliminar foreign keys que referencian `roles`
   - Eliminar primary key existente
   - Renombrar `role_id` a `id`
   - Crear primary key compuesta `(account_id, id)`

2. **Reestructurar `system_user_roles`**:
   - Eliminar constraints existentes
   - Añadir columna `account_id` si no existe
   - Renombrar `user_id` a `system_user_id`
   - Crear primary key compuesta `(account_id, system_user_id, role_id)`

3. **Reestructurar `system_users`**:
   - Eliminar primary key existente
   - Renombrar `user_id` a `id`
   - Crear primary key compuesta `(account_id, id)`

4. **Recrear foreign keys**:
   - `system_user_roles` → `system_users`
   - `system_user_roles` → `roles`
   - `audit_logs` → `system_users`

5. **Función downgrade completa**:
   - Revierte todos los cambios en orden inverso
   - Restaura la estructura original

### Estructura Final

**system_users**:
- Primary Key: `(account_id, id)`
- Columnas: `account_id`, `id`, `email`, `hashed_password`, etc.

**system_user_roles**:
- Primary Key: `(account_id, system_user_id, role_id)`
- Foreign Keys:
  - `(account_id, system_user_id)` → `system_users(account_id, id)`
  - `(account_id, role_id)` → `roles(account_id, id)`

**audit_logs**:
- Nueva columna: `system_user_id`
- Foreign Key: `(account_id, system_user_id)` → `system_users(account_id, id)`

## Validación

✅ **Sintaxis validada**: La migración pasa todas las validaciones de sintaxis
✅ **Funciones completas**: Tanto `upgrade()` como `downgrade()` están implementadas
✅ **Manejo de errores**: Incluye verificaciones de existencia de columnas y constraints

## Próximos Pasos

1. **Ejecutar la migración**:
   ```bash
   cd rayuela_backend
   python -m alembic upgrade head
   ```

2. **Verificar en producción**:
   ```bash
   ./scripts/run-migrations-manual.sh
   ```

3. **Continuar con el deployment**:
   ```bash
   ./scripts/deploy-production.sh --direct --skip-iam --skip-infrastructure
   ```

## Archivos Modificados

- `rayuela_backend/alembic/versions/20250704_120000_add_system_user_id_to_audit_logs.py`
- `rayuela_backend/.env` (creado para testing)
- `rayuela_backend/.env.docker` (creado para testing)
- `rayuela_backend/validate_migration.py` (script de validación)

## Notas Importantes

- La migración maneja tanto casos donde las tablas ya tienen la estructura correcta como casos donde necesitan ser reestructuradas
- Todos los cambios son reversibles usando `alembic downgrade`
- La migración es idempotente - puede ejecutarse múltiples veces sin problemas
- Se mantiene la compatibilidad con el modelo SQLAlchemy existente
