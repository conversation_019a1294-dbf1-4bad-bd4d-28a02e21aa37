from sqlalchemy import (
    Column,
    Integer,
    DateTime,
    ForeignKey,
    Float,
    String,
    UniqueConstraint,
    Index,
    func,
    Enum as SQLAEnum,
    ForeignKeyConstraint,
    PrimaryKeyConstraint,
    Identity,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from src.db.base import Base
from src.db.enums import InteractionType
from .mixins import TenantMixin, ACCOUNT_ID_FK, get_tenant_table_args


class Interaction(Base, TenantMixin):
    __tablename__ = "interactions"

    interaction_id = Column(Integer, Identity(), primary_key=True)  # Match database schema
    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), nullable=False)
    end_user_id = Column(Integer, nullable=False)  # Match database schema
    product_id = Column(Integer, nullable=False)
    interaction_type = Column(SQLAEnum(InteractionType), nullable=False)
    rating = Column(Float, nullable=True)  # Match database schema (was 'value')
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    session_id = Column(String(255), nullable=True)  # Match database schema
    context = Column(JSONB, nullable=True)  # Match database schema (was 'recommendation_metadata')
    created_at = Column(DateTime(timezone=True), server_default=func.now())  # Match database schema
    updated_at = Column(DateTime(timezone=True), server_default=func.now())  # Match database schema

    __table_args__ = get_tenant_table_args(
        # Simple FKs to match database schema
        ForeignKeyConstraint(
            ["end_user_id"],
            ["end_users.end_user_id"],
            ondelete="CASCADE",
            name="interactions_end_user_id_fkey"
        ),
        ForeignKeyConstraint(
            ["product_id"],
            ["products.product_id"],
            ondelete="CASCADE",
            name="interactions_product_id_fkey"
        ),
        Index("idx_interaction_account_timestamp", "account_id", "timestamp"),
        Index("idx_interaction_end_user_timestamp", "end_user_id", "timestamp"),
        Index("idx_interaction_product_timestamp", "product_id", "timestamp"),
        Index("idx_interaction_type", "interaction_type"),
    )

    # Relationships
    account = relationship("Account", back_populates="interactions")
    end_user = relationship(
        "EndUser",
        foreign_keys=[end_user_id],
        back_populates="interactions"
    )
    product = relationship(
        "Product",
        foreign_keys=[product_id],
        back_populates="interactions"
    )
