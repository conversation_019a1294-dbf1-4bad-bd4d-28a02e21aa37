"""consolidated_initial_migration

Revision ID: ea4920022505
Revises:
Create Date: 2025-07-05 02:57:19.735921

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'ea4920022505'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create all tables with correct structure."""
    print("🚀 Starting consolidated initial migration...")

    # Create extensions
    print("📦 Creating PostgreSQL extensions...")
    op.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm;")
    op.execute("CREATE EXTENSION IF NOT EXISTS btree_gin;")

    # Create enums
    print("🏷️ Creating enums...")
    subscription_plan_enum = postgresql.ENUM(
        'FREE', 'BASIC', 'PREMIUM', 'ENTERPRISE',
        name='subscriptionplan',
        create_type=False
    )
    subscription_plan_enum.create(op.get_bind(), checkfirst=True)

    role_type_enum = postgresql.ENUM(
        'ADMIN', 'USER', 'VIEWER',
        name='roletype',
        create_type=False
    )
    role_type_enum.create(op.get_bind(), checkfirst=True)

    # Create accounts table (base table)
    print("🏢 Creating accounts table...")
    op.create_table('accounts',
        sa.Column('account_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('mercadopago_customer_id', sa.String(length=255), nullable=True, comment='Mercado Pago Customer ID'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('onboarding_checklist_status', sa.JSON(), nullable=True, comment='JSON object storing the status of onboarding checklist items'),
        sa.PrimaryKeyConstraint('account_id')
    )
    op.create_index(op.f('ix_accounts_mercadopago_customer_id'), 'accounts', ['mercadopago_customer_id'], unique=False)

    # Create subscriptions table (1:1 with accounts)
    print("💳 Creating subscriptions table...")
    op.create_table('subscriptions',
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('plan_type', postgresql.ENUM('FREE', 'BASIC', 'PREMIUM', 'ENTERPRISE', name='subscriptionplan'), nullable=False),
        sa.Column('api_calls_limit', sa.Integer(), nullable=True),
        sa.Column('storage_limit', sa.BigInteger(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('api_calls_used_current_period', sa.Integer(), nullable=True),
        sa.Column('storage_used_current_period', sa.BigInteger(), nullable=True),
        sa.Column('period_start', sa.DateTime(timezone=True), nullable=True),
        sa.Column('period_end', sa.DateTime(timezone=True), nullable=True),
        sa.Column('mercadopago_subscription_id', sa.String(length=255), nullable=True),
        sa.Column('mercadopago_price_id', sa.String(length=255), nullable=True),
        sa.Column('payment_gateway', sa.String(length=20), nullable=True),
        sa.Column('monthly_api_calls_used', sa.Integer(), nullable=False),
        sa.Column('storage_used', sa.BigInteger(), nullable=False),
        sa.Column('last_reset_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('available_models', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('additional_features', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('last_successful_training_at', sa.DateTime(timezone=True), nullable=True, comment='Fecha del último entrenamiento exitoso'),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('account_id')
    )
    op.create_index(op.f('ix_subscriptions_mercadopago_subscription_id'), 'subscriptions', ['mercadopago_subscription_id'], unique=False)
    op.create_index(op.f('ix_subscriptions_plan_type'), 'subscriptions', ['plan_type'], unique=False)

    # Create API keys table (multi-tenant with composite PK)
    print("🔑 Creating api_keys table...")
    op.create_table('api_keys',
        sa.Column('id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=True, comment='Descriptive name for this API key'),
        sa.Column('api_key_hash', sa.String(length=64), nullable=False, comment='SHA-256 hash of the API key'),
        sa.Column('api_key_prefix', sa.String(length=10), nullable=False, comment='Prefix of the API key for display'),
        sa.Column('api_key_last_chars', sa.String(length=6), nullable=False, comment='Last 6 characters for display'),
        sa.Column('is_active', sa.Boolean(), nullable=False, comment='Whether this API key is active'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('last_used', sa.DateTime(timezone=True), nullable=True, comment='When this API key was last used'),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('account_id', 'id'),
        sa.UniqueConstraint('api_key_hash', name='uq_api_key_hash_global')
    )

    # Create roles table (multi-tenant with composite PK)
    print("👥 Creating roles table...")
    op.create_table('roles',
        sa.Column('id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('name', postgresql.ENUM('ADMIN', 'USER', 'VIEWER', name='roletype'), nullable=False),
        sa.Column('description', sa.String(length=255), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('account_id', 'id')
    )
    op.create_index('idx_role_name', 'roles', ['name'], unique=False)
    op.create_index('idx_role_account', 'roles', ['account_id'], unique=False)

    # Create permissions table (multi-tenant with composite PK)
    print("🔐 Creating permissions table...")
    op.create_table('permissions',
        sa.Column('id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.String(length=255), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('account_id', 'id')
    )

    # Create system_users table (multi-tenant with composite PK)
    print("👤 Creating system_users table...")
    op.create_table('system_users',
        sa.Column('id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('email', sa.String(length=255), nullable=False),
        sa.Column('hashed_password', sa.String(length=255), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_admin', sa.Boolean(), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True, comment='Timestamp of last login by this user'),
        sa.Column('email_verified', sa.Boolean(), nullable=True),
        sa.Column('verification_token', sa.String(length=255), nullable=True),
        sa.Column('verification_token_expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('account_id', 'id')
    )
    op.create_index('idx_system_user_email_global', 'system_users', ['email'], unique=True)
    op.create_index('idx_system_user_account', 'system_users', ['account_id'], unique=False)
    op.create_index('idx_system_users_account_last_login', 'system_users', ['account_id', 'last_login_at'], unique=False)

    # Create role_permissions association table
    print("🔗 Creating role_permissions table...")
    op.create_table('role_permissions',
        sa.Column('role_id', sa.Integer(), nullable=False),
        sa.Column('permission_id', sa.Integer(), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['account_id', 'role_id'], ['roles.account_id', 'roles.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['account_id', 'permission_id'], ['permissions.account_id', 'permissions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('role_id', 'permission_id', 'account_id')
    )

    # Create system_user_roles association table
    print("🔗 Creating system_user_roles table...")
    op.create_table('system_user_roles',
        sa.Column('system_user_id', sa.Integer(), nullable=False),
        sa.Column('role_id', sa.Integer(), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['account_id', 'system_user_id'], ['system_users.account_id', 'system_users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['account_id', 'role_id'], ['roles.account_id', 'roles.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('system_user_id', 'role_id', 'account_id')
    )

    # Create audit_logs table (multi-tenant with composite PK)
    print("📋 Creating audit_logs table...")
    op.create_table('audit_logs',
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
        sa.Column('action', sa.String(), nullable=False),
        sa.Column('entity_type', sa.String(), nullable=False),
        sa.Column('entity_id', sa.Integer(), nullable=False),
        sa.Column('changes', sa.JSON(), nullable=True),
        sa.Column('performed_by', sa.String(), nullable=False),
        sa.Column('system_user_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('details', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['account_id', 'system_user_id'], ['system_users.account_id', 'system_users.id'], name='fk_audit_log_system_user', ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('account_id', 'id')
    )
    op.create_index('idx_audit_account_timestamp', 'audit_logs', ['account_id', 'created_at'], unique=False)
