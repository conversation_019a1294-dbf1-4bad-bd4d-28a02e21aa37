"""Consolidate all table structure fixes and add system_user_id to audit_logs

Revision ID: 20250704_120000
Revises: 20250701_020000
Create Date: 2025-07-04 12:00:00
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = "20250704_120000"
down_revision: Union[str, None] = "20250701_020000"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def _column_exists(table_name: str, column_name: str) -> bool:
    connection = op.get_bind()
    result = connection.execute(
        text(
            """
        SELECT column_name FROM information_schema.columns
        WHERE table_name = :table_name
          AND column_name = :column_name
          AND table_schema = 'public'
        """
        ),
        {"table_name": table_name, "column_name": column_name},
    )
    return result.fetchone() is not None


def _constraint_exists(constraint_name: str, table_name: str) -> bool:
    """Helper function to check if a constraint exists"""
    connection = op.get_bind()
    result = connection.execute(
        text(
            """
        SELECT constraint_name FROM information_schema.table_constraints
        WHERE table_name = :table_name
          AND constraint_name = :constraint_name
          AND table_schema = 'public'
        """
        ),
        {"table_name": table_name, "constraint_name": constraint_name},
    )
    return result.fetchone() is not None


def _table_exists(table_name: str) -> bool:
    """Check if a table exists."""
    connection = op.get_bind()
    result = connection.execute(
        text("""
        SELECT 1 FROM information_schema.tables
        WHERE table_name = :table_name
        """),
        {"table_name": table_name}
    )
    return result.fetchone() is not None


def upgrade() -> None:
    """Consolidate all table structure fixes and add system_user_id to audit_logs."""

    print("🔧 Consolidating table structure fixes...")

    # Step 1: Drop all dependent tables first (since they're empty)
    print("  Dropping dependent tables...")
    tables_to_drop = ['system_user_roles', 'role_permissions']
    for table in tables_to_drop:
        if _table_exists(table):
            op.drop_table(table)
            print(f"    Dropped {table}")

    # Step 2: Drop and recreate roles table with correct structure
    print("  Recreating roles table with composite primary key...")
    if _table_exists('roles'):
        op.drop_table('roles')

    op.create_table('roles',
        sa.Column('id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('account_id', 'id'),
        sa.UniqueConstraint('account_id', 'name', name='uq_role_account_name')
    )
    op.create_index('idx_role_account', 'roles', ['account_id'], unique=False)
    op.create_index('idx_role_name', 'roles', ['name'], unique=False)

    # Step 3: Drop and recreate system_users table with correct structure
    print("  Recreating system_users table with composite primary key...")
    if _table_exists('system_users'):
        op.drop_table('system_users')

    op.create_table('system_users',
        sa.Column('id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('email', sa.String(length=255), nullable=False),
        sa.Column('password_hash', sa.String(length=255), nullable=False),
        sa.Column('first_name', sa.String(length=100), nullable=True),
        sa.Column('last_name', sa.String(length=100), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('account_id', 'id'),
        sa.UniqueConstraint('account_id', 'email', name='uq_system_user_account_email')
    )
    op.create_index('idx_system_user_account', 'system_users', ['account_id'], unique=False)
    op.create_index('idx_system_user_email', 'system_users', ['email'], unique=False)

    # Step 4: Recreate system_user_roles table with correct structure
    print("  Recreating system_user_roles table...")
    op.create_table('system_user_roles',
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('system_user_id', sa.Integer(), nullable=False),
        sa.Column('role_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['account_id', 'system_user_id'], ['system_users.account_id', 'system_users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['account_id', 'role_id'], ['roles.account_id', 'roles.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('account_id', 'system_user_id', 'role_id')
    )

    # Step 5: Recreate role_permissions table with correct structure (if it exists in models)
    print("  Recreating role_permissions table...")
    op.create_table('role_permissions',
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('role_id', sa.Integer(), nullable=False),
        sa.Column('permission_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['account_id', 'role_id'], ['roles.account_id', 'roles.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['account_id', 'permission_id'], ['permissions.account_id', 'permissions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('account_id', 'role_id', 'permission_id')
    )

    # Step 6: Add system_user_id column to audit_logs
    print("  Adding system_user_id column to audit_logs...")
    if not _column_exists("audit_logs", "system_user_id"):
        op.add_column("audit_logs", sa.Column("system_user_id", sa.Integer(), nullable=True))

        # Create foreign key constraint
        op.create_foreign_key(
            "fk_audit_logs_system_user",
            "audit_logs",
            "system_users",
            ["account_id", "system_user_id"],
            ["account_id", "id"],
            ondelete="SET NULL",
        )
        print("    Column and foreign key added successfully!")
    else:
        print("    system_user_id column already exists, skipping...")

    print("✅ All table structure fixes completed successfully!")


def downgrade() -> None:
    """Remove system_user_id column from audit_logs."""

    print("Removing system_user_id column from audit_logs...")

    # Drop system_user_id column from audit_logs
    if _column_exists("audit_logs", "system_user_id"):
        print("  Dropping system_user_id column...")
        op.drop_column("audit_logs", "system_user_id")
        print("  Column dropped successfully!")
    else:
        print("  system_user_id column does not exist, skipping...")

    print("Downgrade completed successfully!")