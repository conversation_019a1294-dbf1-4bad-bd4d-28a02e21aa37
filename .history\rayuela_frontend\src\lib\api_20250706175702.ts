/**
 * New API client using generated code from OpenAPI spec
 * This replaces the manual api.ts file with generated, type-safe functions
 */

// Import generated API functions and types
import {
    getRayuela,
    // Types
    RegisterRequest,
    SystemUserResponse,
    AccountResponse,
    UsageStats,
    PlanInfo,

    ApiKeyResponse,
    NewApiKeyResponse,
    ApiKeyListResponse,
    ApiKeyCreate,
    ApiKeyUpdate,
    GetUsageHistoryApiV1UsageHistoryGet200Item,
    GetUsageSummaryApiV1UsageSummaryGet200,
} from './generated/rayuelaAPI';

// Create a simple ApiError class for backward compatibility
export class ApiError extends Error {
    public status: number;
    public body: unknown;

    constructor(message: string, status: number = 500, body?: unknown) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.body = body;
    }
}

// Helper function to handle API errors in a type-safe way
function handleApiError(error: unknown, defaultMessage: string): never {
    if (error && typeof error === 'object' && 'response' in error) {
        const apiError = error as { response?: { status?: number; data?: unknown }; message?: string };
        const status = apiError.response?.status || 500;
        const message = apiError.message || defaultMessage;
        const body = apiError.response?.data;
        throw new ApiError(message, status, body);
    } else if (error instanceof Error) {
        throw new ApiError(error.message, 500);
    } else {
        throw new ApiError(defaultMessage, 500);
    }
}

// Create a simple API client instance
const api = getRayuela();

// --- Authentication Functions ---

export const loginUser = async (credentials: { email: string; password: string }): Promise<LoginResponse> => {
    try {
        return await api.loginApiV1AuthTokenPost(credentials);
    } catch (error: unknown) {
        handleApiError(error, 'Login failed');
    }
};

export const registerUser = async (
    accountName: string,
    email: string,
    password: string
): Promise<RegisterResponse> => {
    try {
        const registerRequest: RegisterRequest = {
            accountName: accountName,
            email,
            password
        };
        // The backend actually returns RegisterResponse, not Token
        // We cast it to the correct type to fix the API key onboarding bug
        const response = await api.registerApiV1AuthRegisterPost(registerRequest);
        return response as unknown as RegisterResponse;
    } catch (error: unknown) {
        handleApiError(error, 'Registration failed');
    }
};

export const logout = async (): Promise<unknown> => {
    return api.logoutApiV1AuthLogoutPost();
};

export const requestEmailVerification = async (): Promise<unknown> => {
    return api.sendVerificationEmailApiV1AuthSendVerificationEmailPost();
};

export const verifyEmail = async (token: string): Promise<unknown> => {
    return api.verifyEmailApiV1AuthVerifyEmailGet({ token });
};

// --- User Functions ---

export const getCurrentUser = async (): Promise<SystemUserResponse> => {
    try {
        return await api.getCurrentUserInfoApiV1SystemUsersMeGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get current user');
    }
};

// Alias for compatibility
export const getMe = getCurrentUser;

// --- Account Functions ---

export const getCurrentAccount = async (): Promise<AccountResponse> => {
    try {
        // Authentication is handled by the client configuration
        return await api.getAccountInfoApiV1AccountsCurrentGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get current account');
    }
};

// Alias for compatibility
export const getMyAccount = getCurrentAccount;

export const getAccountUsage = async (): Promise<UsageStats> => {
    try {
        return await api.getApiUsageApiV1AccountsUsageGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get account usage');
    }
};

export const getPlans = async (): Promise<Record<string, PlanInfo>> => {
    try {
        return await api.getAvailablePlansApiV1PlansGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get plans');
    }
};

// Alias for compatibility
export const getAvailablePlans = getPlans;

// --- Usage Functions ---

export const getUsageHistory = async (
    startDate?: string,
    endDate?: string
): Promise<unknown[]> => {
    const params: Record<string, string> = {};
    if (startDate) params.start_date = startDate;
    if (endDate) params.end_date = endDate;

    return api.getUsageHistoryApiV1UsageHistoryGet(params);
};

export const getUsageSummary = async (): Promise<GetUsageSummaryApiV1UsageSummaryGet200> => {
    try {
        return await api.getUsageSummaryApiV1UsageSummaryGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get usage summary');
    }
};

// --- API Key Functions ---

export const getApiKeys = async (): Promise<ApiKeyListResponse> => {
    try {
        return await api.listApiKeysApiV1ApiKeysGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get API keys');
    }
};

// Alias for compatibility
export const listApiKeys = getApiKeys;

export const createApiKey = async (apiKeyData: { name: string; permissions?: string[] }): Promise<NewApiKeyResponse> => {
    try {
        const createRequest: ApiKeyCreate = {
            name: apiKeyData.name
        };
        return await api.createApiKeyApiV1ApiKeysPost(createRequest);
    } catch (error: unknown) {
        handleApiError(error, 'Failed to create API key');
    }
};

export const deleteApiKey = async (keyId: string): Promise<void> => {
    try {
        await api.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(Number(keyId));
    } catch (error: unknown) {
        handleApiError(error, 'Failed to delete API key');
    }
};

export const updateApiKey = async (keyId: string, updateData: { name?: string; permissions?: string[] }): Promise<ApiKeyResponse> => {
    try {
        const updateRequest: ApiKeyUpdate = {
            name: updateData.name
        };
        return await api.updateApiKeyApiV1ApiKeysApiKeyIdPut(Number(keyId), updateRequest);
    } catch (error: unknown) {
        handleApiError(error, 'Failed to update API key');
    }
};

export const revokeApiKey = async (apiKeyId: number): Promise<void> => {
    return api.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(apiKeyId);
};

export const getApiKey = async (): Promise<ApiKeyResponse> => {
    return api.getCurrentApiKeyApiV1ApiKeysCurrentGet();
};

export const revokeAllApiKeys = async (): Promise<void> => {
    return api.revokeApiKeyApiV1ApiKeysDelete();
};

// --- Billing Functions ---

export const createCheckoutSession = async (priceId: string): Promise<unknown> => {
    const request = { price_id: priceId };
    return api.createCheckoutSessionApiV1BillingCreateCheckoutSessionPost(request);
};

export const createBillingPortalSession = async (): Promise<unknown> => {
    return api.createPortalSessionApiV1BillingCreatePortalSessionPost({});
};

// --- Health Functions ---

export const healthCheck = async (): Promise<{ status: string }> => {
    try {
        return await api.healthCheckHealthGet();
    } catch (error: unknown) {
        handleApiError(error, 'Health check failed');
    }
};

// --- Analytics Functions ---

export const getRecommendationPerformance = async (
    modelId?: number,
    metricType?: string
): Promise<unknown> => {
    try {
        const params: { model_id?: number; metric_type?: string; account_id?: number } = {};
        if (modelId !== undefined) params.model_id = modelId;
        if (metricType !== undefined) params.metric_type = metricType;
        
        return await api.getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet(params);
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get recommendation performance');
    }
};

export const getConfidenceMetrics = async (): Promise<unknown> => {
    try {
        return await api.getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet();
    } catch (error: unknown) {
        handleApiError(error, 'Failed to get confidence metrics');
    }
};

// --- Missing interfaces for proper API response handling ---

/** 
 * Extended login response interface 
 */
export interface LoginResponse {
    access_token: string;
    token_type: string;
    account_id: number;
    is_admin: boolean;
}

// Export more types for compatibility
export type {
    RegisterRequest,
    SystemUserResponse,
    AccountResponse,
    UsageStats,
    PlanInfo,
    ApiKeyResponse,
    NewApiKeyResponse,
    ApiKeyListResponse,
    ApiKeyCreate,
    ApiKeyUpdate
};

// Fallback interface for RegisterResponse when not present in generated types (static OpenAPI)
export interface RegisterResponse {
    access_token: string;
    token_type: string;
    account_id: number;
    user_id: number;
    is_admin: boolean;
    api_key: string;
    message: string;
}

// Re-export types for backward compatibility
export type ApiKey = ApiKeyResponse;
export type AccountInfo = AccountResponse;
export type UsageHistoryPoint = GetUsageHistoryApiV1UsageHistoryGet200Item;