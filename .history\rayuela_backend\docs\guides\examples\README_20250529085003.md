# Ejemplos de Ingesta de Datos Masiva

Este directorio contiene ejemplos prácticos y scripts para facilitar la ingesta de datos masiva en Rayuela.

## Contenido

### Archivos de Datos de Ejemplo

- **`batch_payload.json`**: Ejemplo completo de payload JSON con usuarios, productos e interacciones
- **`users.csv`**: Plantilla CSV para usuarios
- **`products.csv`**: Plantilla CSV para productos  
- **`interactions.csv`**: Plantilla CSV para interacciones

### Scripts de Ingesta

- **`batch_ingestion_script.py`**: Script completo en Python con manejo de errores y monitoreo
- **`batch_ingestion_script.js`**: Script completo en Node.js con funcionalidades similares

## Uso Rápido

### 1. Configuración

Antes de usar los scripts, configure sus credenciales:

```bash
# Configurar variables de entorno
export RAYUELA_API_KEY="tu_api_key_aqui"
export RAYUELA_API_URL="https://api.rayuela.com/api/v1"
```

### 2. Usando el Script Python

```bash
# Instalar dependencias
pip install requests

# Ejecutar con archivo JSON
python batch_ingestion_script.py --json batch_payload.json

# Ejecutar con archivos CSV
python batch_ingestion_script.py --users users.csv --products products.csv --interactions interactions.csv

# Ver todas las opciones
python batch_ingestion_script.py --help
```

### 3. Usando el Script Node.js

```bash
# Instalar dependencias
npm install axios

# Ejecutar con archivo JSON
node batch_ingestion_script.js --json batch_payload.json

# Ejecutar con archivos CSV
node batch_ingestion_script.js --users users.csv --products products.csv --interactions interactions.csv

# Ver todas las opciones
node batch_ingestion_script.js --help
```

### 4. Usando cURL directamente

```bash
# Enviar payload JSON
curl -X POST "https://api.rayuela.com/api/v1/ingestion/batch" \
  -H "X-API-Key: $RAYUELA_API_KEY" \
  -H "Content-Type: application/json" \
  -d @batch_payload.json

# Verificar estado del trabajo (reemplazar 123 con el job_id recibido)
curl -X GET "https://api.rayuela.com/api/v1/ingestion/batch/123" \
  -H "X-API-Key: $RAYUELA_API_KEY"
```

## Estructura de Datos

### Usuarios (users.csv)

```csv
external_id
user001
user002
user003
```

**Campos:**
- `external_id` (requerido): Identificador único del usuario en su sistema

### Productos (products.csv)

```csv
name,description,price,category,average_rating,num_ratings,inventory_count
Smartphone XYZ,Un smartphone de última generación,499.99,electronics,4.5,120,50
Laptop ABC,Laptop para profesionales,1299.99,electronics,4.8,85,30
```

**Campos:**
- `name` (requerido): Nombre del producto
- `price` (requerido): Precio del producto (decimal > 0)
- `category` (requerido): Categoría del producto
- `description` (opcional): Descripción del producto
- `average_rating` (opcional): Calificación promedio (0.0-5.0)
- `num_ratings` (opcional): Número de calificaciones
- `inventory_count` (opcional): Cantidad en inventario

### Interacciones (interactions.csv)

```csv
user_id,product_id,interaction_type,value
1,1,VIEW,1.0
1,2,PURCHASE,1.0
2,1,RATING,4.5
```

**Campos:**
- `user_id` (requerido): ID numérico del usuario (debe existir en la base de datos)
- `product_id` (requerido): ID numérico del producto (debe existir en la base de datos)
- `interaction_type` (requerido): Tipo de interacción (VIEW, LIKE, PURCHASE, CART, RATING, WISHLIST, CLICK, SEARCH, FAVORITE)
- `value` (requerido): Valor de la interacción (1.0 para la mayoría, 1.0-5.0 para RATING)

## Tipos de Interacción Válidos

| Tipo | Descripción | Valor Típico |
|------|-------------|--------------|
| `VIEW` | Usuario vio el producto | 1.0 |
| `LIKE` | Usuario le dio "me gusta" | 1.0 |
| `PURCHASE` | Usuario compró el producto | cantidad o 1.0 |
| `CART` | Usuario añadió al carrito | cantidad o 1.0 |
| `RATING` | Usuario calificó el producto | 1.0-5.0 |
| `WISHLIST` | Usuario añadió a lista de deseos | 1.0 |
| `CLICK` | Usuario hizo clic en el producto | 1.0 |
| `SEARCH` | Usuario encontró vía búsqueda | 1.0 |
| `FAVORITE` | Usuario marcó como favorito | 1.0 |

## Mejores Prácticas

### 1. Preparación de Datos

- **Validar antes de enviar**: Use los scripts para validar formato antes de la ingesta
- **Identificadores únicos**: Asegúrese de que los `external_id` sean únicos
- **Datos limpios**: Elimine registros duplicados o incompletos
- **Codificación UTF-8**: Use UTF-8 para caracteres especiales

### 2. Orden de Ingesta

1. **Primero usuarios**: Cree usuarios antes que las interacciones
2. **Luego productos**: Cree productos antes que las interacciones  
3. **Finalmente interacciones**: Use los IDs numéricos correctos

### 3. Tamaño de Lotes

- **Lotes moderados**: 1,000-10,000 entidades por lote
- **Monitoreo**: Use los scripts para monitorear el progreso
- **Reintentos**: Implemente reintentos para errores transitorios

### 4. Manejo de Errores

- **Revisar error_details**: Analice errores específicos en la respuesta
- **Corregir y reenviar**: Corrija datos problemáticos y reenvíe
- **Logs**: Mantenga logs de sus operaciones

## Ejemplos de Uso Avanzado

### Ingesta Incremental

```python
# Ejemplo: Ingesta solo de nuevas interacciones
python batch_ingestion_script.py \
  --interactions new_interactions.csv \
  --monitor \
  --retry-on-error
```

### Validación sin Envío

```python
# Ejemplo: Solo validar formato sin enviar
python batch_ingestion_script.py \
  --json batch_payload.json \
  --validate-only
```

### Monitoreo de Trabajo Existente

```python
# Ejemplo: Monitorear trabajo existente
python batch_ingestion_script.py \
  --monitor-job 123
```

## Solución de Problemas

### Error: "user_id X no existe"

**Problema**: Las interacciones referencian IDs de usuario que no existen.

**Solución**: 
1. Asegúrese de crear usuarios primero
2. Use los IDs numéricos correctos (no external_id)
3. Consulte la API para obtener los IDs después de crear usuarios

### Error: "Límites excedidos"

**Problema**: Se excedieron los límites de su plan.

**Solución**:
1. Divida los datos en lotes más pequeños
2. Verifique su plan de suscripción
3. Considere actualizar su plan

### Error: "Formato de datos inválido"

**Problema**: Los datos no cumplen con el esquema requerido.

**Solución**:
1. Use la opción `--validate-only` para verificar formato
2. Revise los campos requeridos en cada entidad
3. Verifique tipos de datos y rangos de valores

## Recursos Adicionales

- [Guía Completa de Ingesta de Datos](../data_ingestion_guide.md)
- [Documentación de API](../../api/)
- [Soporte Técnico](mailto:<EMAIL>)

## Contribuir

Si encuentra errores en los ejemplos o tiene sugerencias de mejora, por favor:

1. Reporte el problema en nuestro sistema de tickets
2. Proporcione ejemplos específicos del problema
3. Incluya logs de error si están disponibles

---

**Nota**: Estos ejemplos están diseñados para facilitar el aprendizaje y la integración. Para uso en producción, considere implementar validaciones adicionales, manejo robusto de errores y logging detallado.
