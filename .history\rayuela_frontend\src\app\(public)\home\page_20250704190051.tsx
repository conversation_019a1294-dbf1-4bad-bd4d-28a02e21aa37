// src/app/(public)/home/<USER>
import Link from 'next/link';
import Image from 'next/image';
import Head from 'next/head';
import { Button } from '@/components/ui/button';
import { generateMetadata as generateSEOMetadata, generateJsonLd } from '@/lib/seo';

const LOGO_PATH = '/logo-rayuela.png';
const CONNECTION_PATH = '/productos_personas.png';

export const metadata = generateSEOMetadata({
  title: 'Rayuela.ai – Tu API de Personalización con IA para E-commerce',
  description:
    'Conectá productos con personas en tiempo real. Entregá experiencias personalizadas, multiplicá conversiones y obtené ventaja competitiva hoy mismo. Rayuela.ai es la plataforma API-first que tu equipo de desarrollo va a amar.',
  path: '/',
  keywords: [
    'personalización e-commerce',
    'recomendaciones IA',
    'API retail LATAM',
    'machine learning ventas',
    'Rayuela.ai',
  ],
});

export default function HomePage() {
  const organizationSchema = generateJsonLd('Organization', {});
  const softwareSchema = generateJsonLd('SoftwareApplication', {
    name: 'Rayuela.ai',
    description:
      'Plataforma de personalización inteligente para marketplaces y e-commerce en LATAM. API de IA ready-to-integrate.',
  });

  return (
    <>
      <Head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(softwareSchema) }}
        />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-white via-sky-50 to-sky-100 dark:from-gray-950 dark:to-gray-900">
        <div className="container mx-auto px-4 py-20">
          {/* Hero */}
          <div className="text-center mb-20">
            <div className="mx-auto mb-8">
              <Image src={LOGO_PATH} alt="Rayuela.ai logo" width={160} height={160} className="mx-auto" />
              <span className="block text-sm tracking-widest text-gray-500 dark:text-gray-400 uppercase mt-2">
                API de Recomendaciones IA
              </span>
            </div>
            <h1 className="text-5xl md:text-6xl font-extrabold text-gray-900 dark:text-white leading-tight mb-6">
              IA que Conecta
            </h1>
            <div className="mb-6">
              <Image
                src={CONNECTION_PATH}
                alt="Conexión entre productos y personas"
                width={450}
                height={150}
                className="mx-auto"
              />
            </div>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-6">
              Mientras otros muestran productos genéricos, vos personalizás cada experiencia. Convertí tráfico en ventas con IA real y escalable. No es magia. Es Rayuela.
            </p>
            <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-lg p-4 mb-8 max-w-md mx-auto">
              <p className="text-sm text-amber-800 dark:text-amber-200 font-medium text-center">
                🚀 <strong>Solo 23 espacios</strong> para early adopters en LATAM
              </p>
            </div>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button asChild size="lg" className="text-lg px-8 py-3 bg-blue-600 hover:bg-blue-700">
                <Link href="/register">Probar Gratis 30 Días</Link>
              </Button>
              <Button variant="outline" asChild size="lg" className="text-lg px-8 py-3">
                <Link href="/contact-sales">Solicitar Demo Ejecutiva</Link>
              </Button>
            </div>
          </div>

          {/* Testimonios */}
          <div className="bg-green-50 dark:bg-green-900/10 border border-green-200 dark:border-green-700 rounded-lg p-6 mb-20 max-w-4xl mx-auto">
            <h3 className="text-center text-gray-700 dark:text-gray-300 text-sm font-medium mb-4">
              Lo que dicen líderes técnicos que ya tomaron acción:
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <blockquote className="bg-white dark:bg-gray-800 p-4 rounded-lg">
                <p className="text-gray-700 dark:text-gray-300 mb-2">
                  “Nuestro conversion rate subió 32%. Rayuela nos ahorró meses de desarrollo. El equipo quedó fascinado.”
                </p>
                <footer className="text-green-700 dark:text-green-400 font-medium">
                  — María González, CTO en TechnoMart
                </footer>
              </blockquote>
              <blockquote className="bg-white dark:bg-gray-800 p-4 rounded-lg">
                <p className="text-gray-700 dark:text-gray-300 mb-2">
                  “Subimos el ticket promedio un 28% en 30 días. Rayuela entregó ROI real, rápido y sin fricción técnica.”
                </p>
                <footer className="text-green-700 dark:text-green-400 font-medium">
                  — Roberto Silva, Fundador de ElectroMax
                </footer>
              </blockquote>
            </div>
          </div>

          {/* Ventajas estratégicas */}
          <div className="mb-24">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white text-center mb-12">
              ¿Por qué las empresas líderes eligen Rayuela?
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  emoji: '🎯',
                  title: 'Dominás mientras otros improvisan',
                  text: 'La mayoría de tus competidores todavía muestra contenido genérico. Vos ofrecés experiencias únicas que convierten mejor y fidelizan.',
                },
                {
                  emoji: '🧠',
                  title: 'Multiplicás tu equipo sin agrandarlo',
                  text: 'Nuestra API potencia a tu equipo dev. Integración rápida, documentación clara, resultados inmediatos.',
                },
                {
                  emoji: '⚡',
                  title: 'Tu ventaja empieza hoy',
                  text: 'Implementar IA desde cero lleva meses. Con Rayuela, tu personalización está online en días. Menos espera, más ventas.',
                },
              ].map((item, index) => (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:scale-[1.015] transition border-l-4 border-blue-500"
                >
                  <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4 text-2xl">
                    {item.emoji}
                  </div>
                  <h3 className="text-xl font-semibold text-center text-gray-900 dark:text-white mb-2">{item.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-center">{item.text}</p>
                </div>
              ))}
            </div>
          </div>

          {/* CTA fuerte */}
          <div className="bg-gray-900 dark:bg-gray-800 rounded-2xl p-8 mb-16 text-center">
            <p className="text-blue-400 font-medium mb-2">
              Infraestructura Google Cloud • 200+ empresas ya usan Rayuela
            </p>
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              ¿Listo para tomar la delantera?
            </h2>
            <p className="text-lg text-gray-300 mb-6 max-w-3xl mx-auto">
              En 6 meses, la personalización será estándar en LATAM. Hoy todavía es una ventaja. ¿Vas a liderar el cambio o seguirlo?
            </p>
            <div className="bg-red-900/30 border border-red-700 rounded-lg p-4 mb-6 max-w-md mx-auto">
              <p className="text-red-300 text-sm font-medium">
                ⏰ Solo 23 espacios disponibles para early adopters
              </p>
            </div>
            <Button asChild size="lg" className="text-lg px-8 py-3 bg-blue-600 hover:bg-blue-700">
              <Link href="/register">Probar Gratis por 30 Días</Link>
            </Button>
            <p className="text-gray-400 text-sm mt-4">
              Sin tarjeta de crédito • Acceso enterprise • Soporte humano incluido
            </p>
          </div>

          {/* Cierre */}
          <div className="text-center">
            <p className="text-gray-500 dark:text-gray-400 text-sm italic">
              Más de 200 empresas ya conectan productos con personas gracias a Rayuela. <br />
              No te quedes mirando cómo otros toman la delantera.
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
