from dotenv import load_dotenv
from pydantic_settings import BaseSettings
from pydantic import (
    field_validator,
    model_validator,
    ValidationError,
    Field,
)
from typing import List, Union
import os
import sys

# Importaciones condicionales de Google Cloud para evitar errores en desarrollo
try:
    from google.cloud import storage, redis_v1, logging as cloud_logging
except ImportError:
    # En desarrollo local, estas librerías pueden no estar disponibles
    storage = None
    redis_v1 = None
    cloud_logging = None


load_dotenv(f".env.{os.getenv('ENV', 'development')}")


class Settings(BaseSettings):
    # Configuración de Pydantic Settings para excluir ALLOWED_ORIGINS del parsing automático
    model_config = {
        "env_ignore": {"ALLOWED_ORIGINS"},  # Excluir ALLOWED_ORIGINS del parsing automático
        "case_sensitive": False,
        "extra": "ignore"
    }

    ENV: str = os.getenv("ENV", "development")
    PROJECT_NAME: str = "Rayuela"
    API_VERSION: str = "v1"

    # Server Configuration
    API_HOST: str = os.getenv("API_HOST", "0.0.0.0")
    API_PORT: int = int(os.getenv("API_PORT", "8001"))

    @field_validator("API_PORT")
    @classmethod
    def validate_api_port(cls, v: int) -> int:
        """Valida que el puerto esté en un rango válido."""
        if not (1024 <= v <= 65535):
            raise ValueError(
                f"API_PORT debe estar entre 1024 y 65535, valor actual: {v}. "
                f"Los puertos por debajo de 1024 requieren privilegios de administrador."
            )
        return v

    # Google Cloud Settings
    GCP_PROJECT_ID: str = os.getenv("GCP_PROJECT_ID", "your-project-id")
    GCP_REGION: str = os.getenv("GCP_REGION", "us-central1")
    GCS_BUCKET_NAME: str = os.getenv("GCS_BUCKET_NAME", "")
    GCS_MODEL_REGISTRY_PATH: str = "models"

    # Data Archival Configuration (GCS only)
    GCS_ARCHIVAL_PATH: str = "archived_data"
    ARCHIVAL_FORMAT: str = os.getenv("ARCHIVAL_FORMAT", "parquet")  # "parquet", "csv", or "json"
    ARCHIVAL_VERIFY_EXPORT: bool = os.getenv("ARCHIVAL_VERIFY_EXPORT", "true").lower() == "true"
    ARCHIVAL_COMPRESSION: str = os.getenv("ARCHIVAL_COMPRESSION", "gzip")  # "gzip", "snappy", or "none"

    # Soft Delete Physical Cleanup Configuration - OPTIMIZED FOR STARTUP COSTS
    SOFT_DELETE_RETENTION_DAYS: int = int(os.getenv("SOFT_DELETE_RETENTION_DAYS", "90"))  # Reduced from 365 to 90 days for cost optimization
    SOFT_DELETE_ARCHIVE_BEFORE_DELETION: bool = os.getenv("SOFT_DELETE_ARCHIVE_BEFORE_DELETION", "true").lower() == "true"
    SOFT_DELETE_BATCH_SIZE: int = int(os.getenv("SOFT_DELETE_BATCH_SIZE", "1000"))  # Smaller batches for safety

    # Data Archival Configuration
    ARCHIVAL_STRATEGY: str = os.getenv("ARCHIVAL_STRATEGY", "gcs")  # "gcs" or "bigquery"
    ARCHIVAL_FORMAT: str = os.getenv("ARCHIVAL_FORMAT", "parquet")  # "parquet", "csv", or "json"
    ARCHIVAL_BUCKET_PATH: str = "archived_data"
    ARCHIVAL_VERIFY_EXPORT: bool = os.getenv("ARCHIVAL_VERIFY_EXPORT", "true").lower() == "true"
    API_BASE_URL: str = os.getenv("API_BASE_URL", "")

    # Security Settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "")
    ALGORITHM: str = "HS256"  # Algoritmo estándar para JWT
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    MAX_LOGIN_ATTEMPTS: int = 5
    LOGIN_COOLDOWN_MINUTES: int = 15

    # Rate Limiting
    DEFAULT_RATE_LIMIT: int = int(os.getenv("RATE_LIMIT", "100"))
    DEFAULT_RATE_LIMIT_PERIOD: int = int(os.getenv("RATE_LIMIT_PERIOD", "60"))

    # Redis Configuration
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    CACHE_EXPIRE_TIME: int = int(os.getenv("CACHE_EXPIRE_TIME", "30"))
    CACHE_PREFIX: str = "rayuela-cache"
    CACHE_MAX_SIZE: int = 1000
    CACHE_TTL: int = 3600

    # Celery Configuration
    CELERY_BROKER_URL: str = os.getenv("CELERY_BROKER_URL", REDIS_URL)
    CELERY_RESULT_BACKEND: str = os.getenv("CELERY_RESULT_BACKEND", REDIS_URL)
    CELERY_TASK_TRACK_STARTED: bool = True
    CELERY_TASK_TIME_LIMIT: int = 3600 * 2  # 2 hours
    CELERY_WORKER_MAX_TASKS_PER_CHILD: int = 10  # Restart worker after 10 tasks
    CELERY_WORKER_PREFETCH_MULTIPLIER: int = 1  # Don't prefetch more than one task

    # Database Configuration
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "rayuela_migration")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "DBpass2025!")
    POSTGRES_SERVER: str = os.getenv("POSTGRES_SERVER", "localhost")
    POSTGRES_PORT: int = int(os.getenv("POSTGRES_PORT", "5432"))
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "recommender_db")
    PARTITION_SIZE: int = 100_000

    # Celery Task Queues
    CELERY_TASK_DEFAULT_QUEUE: str = "default"
    CELERY_TASK_AUDIT_QUEUE: str = "audit"

    # Payment Gateway Configuration
    # Stripe Configuration (legacy)
    STRIPE_API_KEY: str = os.getenv("STRIPE_API_KEY", "")
    STRIPE_WEBHOOK_SECRET: str = os.getenv("STRIPE_WEBHOOK_SECRET", "")
    STRIPE_PRICE_STARTER: str = os.getenv("STRIPE_PRICE_STARTER", "")
    STRIPE_PRICE_PRO: str = os.getenv("STRIPE_PRICE_PRO", "")
    STRIPE_PRICE_ENTERPRISE: str = os.getenv("STRIPE_PRICE_ENTERPRISE", "")

    # Mercado Pago Configuration
    MERCADOPAGO_ACCESS_TOKEN: str = os.getenv("MERCADOPAGO_ACCESS_TOKEN", "")
    MERCADOPAGO_PUBLIC_KEY: str = os.getenv("MERCADOPAGO_PUBLIC_KEY", "")
    MERCADOPAGO_WEBHOOK_SECRET: str = ""  # Se cargará desde GCP Secret Manager
    MERCADOPAGO_PRICE_STARTER: str = os.getenv("MERCADOPAGO_PRICE_STARTER", "")
    MERCADOPAGO_PRICE_PRO: str = os.getenv("MERCADOPAGO_PRICE_PRO", "")
    MERCADOPAGO_PRICE_ENTERPRISE: str = os.getenv("MERCADOPAGO_PRICE_ENTERPRISE", "")

    # Mercado Pago Price Amounts (in ARS)
    MERCADOPAGO_PRICE_STARTER_AMOUNT: float = float(os.getenv("MERCADOPAGO_PRICE_STARTER_AMOUNT", "2500.0"))
    MERCADOPAGO_PRICE_PRO_AMOUNT: float = float(os.getenv("MERCADOPAGO_PRICE_PRO_AMOUNT", "5000.0"))
    MERCADOPAGO_PRICE_ENTERPRISE_AMOUNT: float = float(os.getenv("MERCADOPAGO_PRICE_ENTERPRISE_AMOUNT", "15000.0"))

    # Common Configuration
    FRONTEND_URL: str = os.getenv("FRONTEND_URL", "http://localhost:3000")
    PAYMENT_GATEWAY: str = os.getenv("PAYMENT_GATEWAY", "mercadopago")  # "stripe" or "mercadopago"

    @property
    def STRIPE_PRICE_IDS(self) -> dict:
        """Returns a dictionary of Stripe price IDs for each plan."""
        return {
            "STARTER": self.STRIPE_PRICE_STARTER,
            "PRO": self.STRIPE_PRICE_PRO,
            "ENTERPRISE": self.STRIPE_PRICE_ENTERPRISE
        }

    @property
    def MERCADOPAGO_PRICE_IDS(self) -> dict:
        """Returns a dictionary of Mercado Pago price IDs for each plan."""
        return {
            "STARTER": self.MERCADOPAGO_PRICE_STARTER,
            "PRO": self.MERCADOPAGO_PRICE_PRO,
            "ENTERPRISE": self.MERCADOPAGO_PRICE_ENTERPRISE
        }

    @property
    def PRICE_IDS(self) -> dict:
        """Returns a dictionary of price IDS for each plan based on the configured payment gateway."""
        if self.PAYMENT_GATEWAY == "stripe":
            return self.STRIPE_PRICE_IDS
        else:
            return self.MERCADOPAGO_PRICE_IDS

    @property
    def MERCADOPAGO_PRICE_AMOUNTS(self) -> dict:
        """Returns a dictionary of Mercado Pago price amounts for each plan."""
        return {
            "STARTER": self.MERCADOPAGO_PRICE_STARTER_AMOUNT,
            "PRO": self.MERCADOPAGO_PRICE_PRO_AMOUNT,
            "ENTERPRISE": self.MERCADOPAGO_PRICE_ENTERPRISE_AMOUNT
        }

    # CORS and Host Configuration
    # NOTA: ALLOWED_ORIGINS se maneja por separado para evitar problemas de parsing con Pydantic Settings
    # Ver get_allowed_origins() más abajo

    # ALLOWED_HOSTS: Lista de hosts permitidos para TrustedHostMiddleware.
    # En producción, especificar los dominios exactos de la aplicación.
    # Ejemplo: ["api.example.com", "backend.example.com"]
    # ALLOWED_HOSTS: Lista de hosts permitidos para TrustedHostMiddleware.
    # Se obtiene de la variable de entorno ALLOWED_HOSTS, con fallbacks para desarrollo
    @property
    def ALLOWED_HOSTS(self) -> List[str]:
        hosts_env = os.getenv("ALLOWED_HOSTS", "")
        if hosts_env:
            # Si hay variable de entorno, parsear como lista separada por comas
            hosts = [host.strip() for host in hosts_env.split(",") if host.strip()]
            return hosts

        # Fallback para desarrollo
        return [
            "localhost",
            "127.0.0.1",
        ]

    # Límites por defecto
    DEFAULT_USERS_LIMIT: int = 10000
    DEFAULT_ITEMS_LIMIT: int = 100000
    DEFAULT_STORAGE_LIMIT: int = 1024 * 1024 * 1024  # 1GB
    DEFAULT_BATCH_LIMIT: int = 1000000  # 1M registros por batch

    # ML Model Defaults
    # Collaborative Filtering
    COLLABORATIVE_FACTORS: int = int(os.getenv("COLLABORATIVE_FACTORS", "50"))
    COLLABORATIVE_REGULARIZATION: float = float(
        os.getenv("COLLABORATIVE_REGULARIZATION", "0.01")
    )
    COLLABORATIVE_ITERATIONS: int = int(os.getenv("COLLABORATIVE_ITERATIONS", "15"))
    # Content-Based
    CONTENT_VECTOR_SIZE: int = int(os.getenv("CONTENT_VECTOR_SIZE", "100"))
    CONTENT_WINDOW: int = int(os.getenv("CONTENT_WINDOW", "5"))
    CONTENT_MIN_COUNT: int = int(os.getenv("CONTENT_MIN_COUNT", "1"))
    CONTENT_WORKERS: int = int(os.getenv("CONTENT_WORKERS", "4"))

    # @field_validator("DATABASE_URL")
    # def validate_database_url(cls, v):
    #     if not v:
    #         raise ValueError("DATABASE_URL cannot be empty")
    #     if not (
    #         v.get_secret_value().startswith("postgresql://")
    #         or v.get_secret_value().startswith("postgresql+asyncpg://")
    #     ):
    #         raise ValueError("DATABASE_URL must be a PostgreSQL connection string")
    #     return v

    @field_validator("SECRET_KEY")
    @classmethod
    def validate_secret_key(cls, v: str) -> str:
        """Valida SECRET_KEY con reglas estrictas salvo en workers de migración.

        Durante la ejecución de jobs de migración (`WORKER_TYPE=migration`) la clave
        no se usa para firmar JWT ni exponer endpoints, por lo que una validación
        mínima es suficiente para evitar que el pipeline falle por un secreto de
        ejemplo. En todos los demás casos se mantiene la validación reforzada.
        """

        # Permitir claves más cortas cuando solo se está ejecutando un job de
        # migración. Esto evita fallas en Cloud Build si el SECRET_KEY aún no
        # cumple con los requisitos de producción.
        if os.getenv("WORKER_TYPE") == "migration":
            if not v or len(v) < 8:
                raise ValueError("SECRET_KEY must be at least 8 characters long")
            return v

        # Validación estricta (API, workers, etc.)
        if not v:
            raise ValueError(
                "SECRET_KEY must be set in environment variables or Secret Manager"
            )

        # Verificar longitud mínima (64 caracteres = ~384 bits de entropía con base64)
        if len(v) < 32:
            raise ValueError(
                "SECRET_KEY must be at least 32 characters long (64+ recommended)"
            )

        # Verificar si parece ser una clave de desarrollo/ejemplo
        common_dev_keys = [
            "your-secret-key",
            "your_secret_key",
            "secret",
            "change_this",
            "admin",
            "development",
            "test",
            "example",
        ]

        lower_v = v.lower()
        if any(dev_key in lower_v for dev_key in common_dev_keys):
            if os.getenv("ENV") == "production":
                raise ValueError(
                    "SECRET_KEY appears to be a development key. Use a strong random key in production."
                )
            else:
                import warnings
                warnings.warn(
                    "SECRET_KEY appears to be a development key. This is fine for development but use a strong random key in production."
                )

        # Entropía básica (caracteres únicos)
        if len(set(v)) < 20 and len(v) >= 32:
            if os.getenv("ENV") == "production":
                raise ValueError("SECRET_KEY has low entropy. Use a strong random key in production.")
            else:
                import warnings
                warnings.warn(
                    "SECRET_KEY has low entropy. This is fine for development but use a strong random key in production."
                )

        return v

    @field_validator("GCP_PROJECT_ID", "GCS_BUCKET_NAME")
    @classmethod
    def validate_gcp_settings(cls, v: str) -> str:
        if not v and os.getenv("ENV") == "production":
            raise ValueError(
                "GCP settings must be configured in production environment"
            )
        return v

    @field_validator("REDIS_URL")
    @classmethod
    def validate_redis_url(cls, v: str) -> str:
        """Valida que la URL de Redis tenga el formato correcto."""
        if not v:
            raise ValueError("REDIS_URL cannot be empty")

        # Verificar formato básico de URL de Redis
        if not (v.startswith("redis://") or v.startswith("rediss://")):
            raise ValueError(
                "REDIS_URL must start with redis:// or rediss:// (for SSL)"
            )

        # Verificar si es una URL de desarrollo/ejemplo en producción
        if os.getenv("ENV") == "production" and ("localhost" in v or "127.0.0.1" in v):
            raise ValueError(
                "Using localhost Redis in production environment is not recommended"
            )

        return v

    # ALLOWED_HOSTS se maneja como propiedad dinámica - no necesita validadores de campo

    @property
    def database_url(self) -> str:
        """Construye la URL de conexión a la base de datos"""
        from urllib.parse import quote_plus
        # URL-encode the password to handle special characters
        encoded_password = quote_plus(self.POSTGRES_PASSWORD)
        return f"postgresql+asyncpg://{self.POSTGRES_USER}:{encoded_password}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"

    @property
    def database_connection_params(self) -> dict:
        """Devuelve parámetros de conexión individuales para evitar problemas de parsing de URL"""
        return {
            "host": self.POSTGRES_SERVER,
            "port": self.POSTGRES_PORT,
            "user": self.POSTGRES_USER,
            "password": self.POSTGRES_PASSWORD,
            "database": self.POSTGRES_DB,
        }

    @property
    def sqlalchemy_database_url(self) -> str:
        """Construye la URL de SQLAlchemy usando parámetros seguros"""
        from urllib.parse import quote_plus
        # URL-encode todos los componentes para máxima seguridad
        encoded_user = quote_plus(self.POSTGRES_USER)
        encoded_password = quote_plus(self.POSTGRES_PASSWORD)
        encoded_host = quote_plus(self.POSTGRES_SERVER)
        encoded_db = quote_plus(self.POSTGRES_DB)
        return f"postgresql+asyncpg://{encoded_user}:{encoded_password}@{encoded_host}:{self.POSTGRES_PORT}/{encoded_db}"

    @property
    def storage_client(self):
        if os.getenv("ENV") == "production":
            return storage.Client()
        return storage.Client.create_anonymous_client()

    @property
    def logging_client(self):
        if os.getenv("ENV") == "production":
            return cloud_logging.Client()
        return None

    @property
    def redis_client(self):
        if os.getenv("ENV") == "production":
            return redis_v1.CloudRedisClient()
        return None

    # API Key settings
    API_KEY_USAGE_UPDATE_INTERVAL: int = 60 * 15  # En segundos, 15 minutos

    class ConfigDict:
        env_file = f".env.{os.getenv('ENV', 'development')}"
        case_sensitive = True
        env_file_encoding = "utf-8"


    def load_secrets_from_gcp(self):
        """
        Carga secretos desde Google Cloud Secret Manager.
        Solo se ejecuta en entorno de producción.
        """
        try:
            from google.cloud import secretmanager

            # Crear cliente de Secret Manager
            client = secretmanager.SecretManagerServiceClient()

            # Lista completa de secretos que deben cargarse desde Secret Manager
            # Incluye credenciales de infraestructura y servicios de terceros
            secrets_to_load = {
                # Credenciales de base de datos
                "POSTGRES_SERVER": "POSTGRES_SERVER",
                "POSTGRES_USER": "POSTGRES_USER",
                "POSTGRES_DB": "POSTGRES_DB",
                "POSTGRES_PORT": "POSTGRES_PORT",
                "APP_DB_PASSWORD": "APP_DB_PASSWORD",
                "MAINTENANCE_DB_PASSWORD": "MAINTENANCE_DB_PASSWORD",

                # Credenciales de Redis
                "REDIS_PASSWORD": "REDIS_PASSWORD",
                "REDIS_HOST": "REDIS_HOST",
                "REDIS_PORT": "REDIS_PORT",
                "REDIS_URL": "REDIS_URL",

                # Configuración de seguridad
                "SECRET_KEY": "SECRET_KEY",

                # Configuración de Google Cloud
                "GCS_BUCKET_NAME": "GCS_BUCKET_NAME",

                # Credenciales de MercadoPago
                "MERCADOPAGO_ACCESS_TOKEN": "MERCADOPAGO_ACCESS_TOKEN",
                "MERCADOPAGO_PUBLIC_KEY": "MERCADOPAGO_PUBLIC_KEY",
                "MERCADOPAGO_WEBHOOK_SECRET": "MERCADOPAGO_WEBHOOK_SECRET",

                # Credenciales de Stripe (legacy)
                "STRIPE_API_KEY": "STRIPE_API_KEY",
                "STRIPE_WEBHOOK_SECRET": "STRIPE_WEBHOOK_SECRET",
            }

            # Cargar cada secreto desde GCP Secret Manager
            loaded_secrets = 0
            failed_secrets = []

            for secret_name, attr_name in secrets_to_load.items():
                try:
                    secret_path = f"projects/{self.GCP_PROJECT_ID}/secrets/{secret_name}/versions/latest"
                    response = client.access_secret_version(name=secret_path)
                    secret_value = response.payload.data.decode("UTF-8")

                    # Establecer el secreto como atributo en la instancia de Settings
                    setattr(self, attr_name, secret_value)
                    loaded_secrets += 1

                    # No registrar el valor del secreto, solo que se cargó correctamente
                    import logging
                    logging.info(f"Cargado secreto: {secret_name}")
                except Exception as e:
                    # Registrar error específico pero continuar con otros secretos
                    failed_secrets.append(secret_name)
                    import logging
                    logging.error(f"Error al cargar secreto {secret_name}: {str(e)}")

            # Validar que se cargaron los secretos críticos
            critical_secrets = ["POSTGRES_PASSWORD", "SECRET_KEY", "POSTGRES_SERVER", "REDIS_HOST"]
            missing_critical = [s for s in critical_secrets if s in failed_secrets]

            if missing_critical:
                import logging
                logging.error(f"Faltan secretos críticos: {missing_critical}")
                return False

            import logging
            logging.info(f"Secretos cargados exitosamente: {loaded_secrets}/{len(secrets_to_load)}")
            if failed_secrets:
                logging.warning(f"Secretos no encontrados (usando valores por defecto): {failed_secrets}")

            return True

        except Exception as e:
            import logging
            logging.error(f"Error general al cargar secretos desde GCP Secret Manager: {str(e)}")
            # No lanzar excepción aquí, el caller (lifespan) se encargará de manejarla
            return False


try:
    settings = Settings()
except ValidationError as e:
    print("Error in configuration:")
    for error in e.errors():
        print(f"- {error['loc'][0]}: {error['msg']}")
    sys.exit(1)


def get_allowed_origins() -> List[str]:
    """
    Obtiene la lista de orígenes permitidos para CORS de forma segura.
    Esta función maneja ALLOWED_ORIGINS por separado para evitar problemas
    de parsing con Pydantic Settings.
    """
    try:
        # Obtener valor directo de la variable de entorno
        allowed_origins_env = os.getenv("ALLOWED_ORIGINS", "")

        if allowed_origins_env:
            # Limpiar caracteres problemáticos
            import re
            clean_value = re.sub(r'[\x00-\x1f\x7f-\x9f\ufeff\r\n]', '', allowed_origins_env).strip()

            if clean_value:
                # Convertir a lista
                if "," in clean_value:
                    origins_list = [item.strip() for item in clean_value.split(",") if item.strip()]
                else:
                    origins_list = [clean_value]

                print(f"✅ ALLOWED_ORIGINS configurado desde env: {origins_list}")
                return origins_list

        # Si no hay valor en env o está vacío, usar valores por defecto
        if os.getenv("ENV") == "production":
            # En producción, DEBE haber una variable FRONTEND_URL configurada
            frontend_url = os.getenv("FRONTEND_URL")
            if not frontend_url:
                print("⚠️ ADVERTENCIA: FRONTEND_URL no configurada en producción. CORS puede fallar.")
                return []

            origins_list = [frontend_url]
            print(f"✅ ALLOWED_ORIGINS configurado desde FRONTEND_URL: {origins_list}")
            return origins_list
        else:
            # Valores por defecto para desarrollo
            dev_origins = [
                "http://localhost:3000",
                "http://127.0.0.1:3000",
                "http://localhost:5173",  # Vite dev server
                "http://127.0.0.1:5173"   # Vite dev server
            ]
            print(f"✅ ALLOWED_ORIGINS configurado por defecto para desarrollo: {dev_origins}")
            return dev_origins

    except Exception as e:
        print(f"⚠️ Error obteniendo ALLOWED_ORIGINS: {e}")
        # Valor por defecto seguro en caso de error
        if os.getenv("ENV") == "production":
            return ["https://rayuela-frontend-1002953244539.us-central1.run.app"]
        else:
            return [
                "http://localhost:3000",
                "http://127.0.0.1:3000",
                "http://localhost:5173",
                "http://127.0.0.1:5173"
            ]
