#!/usr/bin/env python3
"""
Ejemplo de uso de la API de ingesta masiva con nomenclatura camelCase consistente.

Este script demuestra cómo enviar datos a la API de ingesta masiva usando
la nomenclatura camelCase en todos los niveles del payload.

Después de la corrección de la inconsistencia crítica, ahora tanto los campos
de nivel superior como los anidados usan camelCase de manera consistente.
"""

import requests
import json
from typing import Dict, Any


class RayuelaIngestionClient:
    """Cliente para la API de ingesta masiva de Rayuela con soporte camelCase."""
    
    def __init__(self, api_key: str, base_url: str = "https://api.rayuela.ai/api/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "X-API-Key": api_key,
            "Content-Type": "application/json"
        }
    
    def start_batch_ingestion(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Inicia un trabajo de ingesta masiva.
        
        Args:
            data: Datos en formato camelCase consistente
            
        Returns:
            Respuesta de la API con información del trabajo creado
        """
        url = f"{self.base_url}/ingestion/batch"
        response = requests.post(url, headers=self.headers, json=data)
        
        if response.status_code == 202:
            return response.json()
        else:
            response.raise_for_status()


def main():
    """Ejemplo de uso del cliente de ingesta masiva."""
    
    # Configuración
    API_KEY = "tu_api_key_aqui"  # Reemplaza con tu API key real
    
    # Datos de ejemplo con nomenclatura camelCase consistente
    batch_data = {
        "users": [
            {
                "externalId": "user_001",
                "preferredCategories": ["electronics", "books"],
                "dislikedCategories": ["sports"],
                "preferredBrands": ["Apple", "Samsung"],
                "priceRangeMin": 10.50,
                "priceRangeMax": 500.00,
                "demographicInfo": {
                    "age": 28,
                    "gender": "M",
                    "location": "urban"
                },
                "onboardingPreferences": {
                    "shoppingFrequency": "weekly",
                    "interests": ["technology", "fashion"]
                }
            }
        ],
        "products": [
            {
                "externalId": "prod_001",
                "name": "Smartphone XYZ",
                "description": "Un smartphone de última generación",
                "price": 599.99,
                "category": "electronics",
                "averageRating": 4.5,
                "numRatings": 120,
                "inventoryCount": 50
            }
        ],
        "interactions": [
            {
                "userId": 1,
                "productId": 1,
                "interactionType": "VIEW",
                "value": 1.0,
                "recommendationMetadata": {
                    "source": "homepage",
                    "algorithm": "collaborative_filtering",
                    "confidence": 0.85
                }
            }
        ]
    }
    
    # Crear cliente e iniciar ingesta
    client = RayuelaIngestionClient(API_KEY)
    
    try:
        result = client.start_batch_ingestion(batch_data)
        
        print("✅ Ingesta iniciada correctamente!")
        print(f"📋 Job ID: {result['job_id']}")
        print(f"📊 Total usuarios: {result['total_users']}")
        print(f"📦 Total productos: {result['total_products']}")
        print(f"🔗 Total interacciones: {result['total_interactions']}")
        print(f"📝 Estado: {result['status']}")
        
        if result.get('task_id'):
            print(f"🔄 Task ID: {result['task_id']}")
            
    except requests.exceptions.HTTPError as e:
        print(f"❌ Error HTTP: {e}")
        print(f"📄 Respuesta: {e.response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")


def validate_camelcase_format():
    """
    Función de utilidad para validar que los datos están en formato camelCase.
    
    Esta función demuestra cómo verificar que los datos siguen la nomenclatura
    camelCase antes de enviarlos a la API.
    """
    
    # Campos que deben estar en camelCase
    expected_user_fields = [
        "externalId", "preferredCategories", "dislikedCategories",
        "preferredBrands", "dislikedBrands", "priceRangeMin", "priceRangeMax",
        "demographicInfo", "onboardingPreferences"
    ]
    
    expected_product_fields = [
        "externalId", "averageRating", "numRatings", "inventoryCount"
    ]
    
    expected_interaction_fields = [
        "userId", "productId", "interactionType", "recommendationMetadata"
    ]
    
    print("📋 Campos esperados en camelCase:")
    print(f"👤 Usuarios: {expected_user_fields}")
    print(f"📦 Productos: {expected_product_fields}")
    print(f"🔗 Interacciones: {expected_interaction_fields}")


if __name__ == "__main__":
    print("🚀 Ejemplo de Ingesta Masiva con camelCase Consistente")
    print("=" * 60)
    
    validate_camelcase_format()
    print()
    
    # Descomenta la siguiente línea para ejecutar el ejemplo real
    # main()
    
    print("💡 Recuerda reemplazar 'tu_api_key_aqui' con tu API key real")
    print("💡 Descomenta la línea main() para ejecutar el ejemplo")
