#!/usr/bin/env python3
"""
Script para validar la sintaxis de la migración sin ejecutarla en una base de datos real.
Verifica que la migración se pueda importar y que las funciones estén bien definidas.
"""

import sys
import os
import importlib.util
from pathlib import Path

# Agregar el directorio src al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def validate_migration_file(migration_path):
    """Valida que el archivo de migración tenga la sintaxis correcta."""
    
    print(f"🔍 Validando migración: {migration_path}")
    
    try:
        # Cargar el módulo de migración
        spec = importlib.util.spec_from_file_location("migration", migration_path)
        migration_module = importlib.util.module_from_spec(spec)
        
        # Intentar ejecutar el módulo (esto verificará la sintaxis)
        spec.loader.exec_module(migration_module)
        
        # Verificar que las funciones requeridas existan
        required_functions = ['upgrade', 'downgrade']
        missing_functions = []
        
        for func_name in required_functions:
            if not hasattr(migration_module, func_name):
                missing_functions.append(func_name)
        
        if missing_functions:
            print(f"❌ Faltan funciones requeridas: {missing_functions}")
            return False
        
        # Verificar que las funciones sean callable
        for func_name in required_functions:
            func = getattr(migration_module, func_name)
            if not callable(func):
                print(f"❌ {func_name} no es una función")
                return False
        
        # Verificar metadatos de la migración
        required_metadata = ['revision', 'down_revision']
        missing_metadata = []
        
        for metadata in required_metadata:
            if not hasattr(migration_module, metadata):
                missing_metadata.append(metadata)
        
        if missing_metadata:
            print(f"⚠️  Falta metadata: {missing_metadata}")
        
        print("✅ Migración válida - sintaxis correcta")
        print(f"   - Revision: {getattr(migration_module, 'revision', 'N/A')}")
        print(f"   - Down revision: {getattr(migration_module, 'down_revision', 'N/A')}")
        print(f"   - Funciones: {', '.join(required_functions)}")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ Error de sintaxis: {e}")
        return False
    except ImportError as e:
        print(f"❌ Error de importación: {e}")
        return False
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        return False

def main():
    """Función principal."""
    
    print("🔧 Validador de Migraciones de Rayuela")
    print("=" * 50)
    
    # Buscar el archivo de migración específico
    migration_file = "alembic/versions/20250704_120000_add_system_user_id_to_audit_logs.py"
    migration_path = Path(__file__).parent / migration_file
    
    if not migration_path.exists():
        print(f"❌ No se encontró el archivo de migración: {migration_path}")
        return 1
    
    # Validar la migración
    if validate_migration_file(migration_path):
        print("\n🎉 Validación exitosa!")
        print("La migración tiene sintaxis correcta y puede ser ejecutada.")
        return 0
    else:
        print("\n❌ Validación fallida!")
        print("La migración tiene errores que deben ser corregidos.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
