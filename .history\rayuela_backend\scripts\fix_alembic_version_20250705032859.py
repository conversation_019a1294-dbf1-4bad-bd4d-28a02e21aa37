#!/usr/bin/env python3
"""
Script para actualizar la tabla alembic_version para que apunte a la nueva migración consolidada.

Este script:
1. Se conecta a la base de datos
2. Actualiza la tabla alembic_version para que apunte a la nueva migración consolidada
3. Verifica que el cambio se aplicó correctamente
"""

import asyncio
import os
import sys
from pathlib import Path

# Agregar el directorio src al path para importar módulos
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from db.database import get_database_url
import asyncpg


async def fix_alembic_version():
    """Actualiza la tabla alembic_version para apuntar a la migración consolidada."""
    
    # Obtener la URL de la base de datos
    database_url = get_database_url()
    print(f"🔗 Conectando a la base de datos...")
    
    try:
        # Conectar a la base de datos
        conn = await asyncpg.connect(database_url)
        print("✅ Conexión exitosa")
        
        # Verificar el estado actual
        current_version = await conn.fetchval("SELECT version_num FROM alembic_version LIMIT 1")
        print(f"📋 Versión actual en alembic_version: {current_version}")
        
        # Nueva versión consolidada
        new_version = "ea4920022505"  # ID de nuestra migración consolidada
        print(f"🎯 Nueva versión objetivo: {new_version}")
        
        if current_version == new_version:
            print("✅ La versión ya está actualizada, no se requiere cambio")
            return
        
        # Actualizar la tabla alembic_version
        print("🔄 Actualizando tabla alembic_version...")
        await conn.execute(
            "UPDATE alembic_version SET version_num = $1",
            new_version
        )
        
        # Verificar el cambio
        updated_version = await conn.fetchval("SELECT version_num FROM alembic_version LIMIT 1")
        print(f"✅ Versión actualizada: {updated_version}")
        
        if updated_version == new_version:
            print("🎉 ¡Actualización exitosa!")
        else:
            print("❌ Error: La actualización no se aplicó correctamente")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error al actualizar alembic_version: {e}")
        sys.exit(1)
    finally:
        if 'conn' in locals():
            await conn.close()
            print("🔒 Conexión cerrada")


if __name__ == "__main__":
    print("🔧 SCRIPT DE REPARACIÓN DE ALEMBIC VERSION")
    print("=" * 50)
    asyncio.run(fix_alembic_version())
