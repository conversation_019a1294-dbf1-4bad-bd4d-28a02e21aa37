steps:
  # 1. Build Backend Image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-backend'
    args:
      - 'build'
      - '-f'
      - 'rayuela_backend/Dockerfile'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$COMMIT_SHA'
      - './rayuela_backend'
    waitFor: ['-']

  # 2. Push Backend Image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-backend'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$COMMIT_SHA'
    waitFor: ['build-backend']

  # 3. Run Database Migrations
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'run-migrations'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🔄 EJECUTANDO MIGRACIONES DE BASE DE DATOS"
        echo "============================================"
        
        # Delete previous job if exists
        gcloud run jobs delete rayuela-migrations --region=us-central1 --quiet 2>/dev/null || echo "✅ No hay job anterior"

        # Create job from backend image built in this pipeline
        gcloud run jobs create rayuela-migrations \
            --image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$COMMIT_SHA \
            --region=us-central1 \
            --task-timeout=1800 \
            --memory=2Gi \
            --cpu=1 \
            --max-retries=1 \
            --parallelism=1 \
            --set-env-vars=ENV=production,PYTHONPATH=/app/src,WORKER_TYPE=migration,GCP_PROJECT_ID=$PROJECT_ID \
            --set-secrets=POSTGRES_USER=POSTGRES_USER:latest,POSTGRES_DB=POSTGRES_DB:latest,POSTGRES_SERVER=POSTGRES_SERVER:latest,POSTGRES_PORT=POSTGRES_PORT:latest,POSTGRES_PASSWORD=POSTGRES_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest,GCS_BUCKET_NAME=GCS_BUCKET_NAME:latest,REDIS_URL=REDIS_URL:latest,ALLOWED_ORIGINS=ALLOWED_ORIGINS:latest \
            --vpc-connector=rayuela-vpc-connector \
            --service-account=rayuela-backend-sa@$PROJECT_ID.iam.gserviceaccount.com \
            --command=bash \
            --args="-c,cd /app && python check_deployment.py && pip install --no-cache-dir alembic==1.13.1 && python -m alembic -c alembic.ini upgrade head"

        echo "🚀 Ejecutando job de migración..."
        gcloud run jobs execute rayuela-migrations --region=us-central1 --wait

        echo "🧹 Limpiando job temporal..."
        gcloud run jobs delete rayuela-migrations --region=us-central1 --quiet || echo "⚠️ No se pudo limpiar el job"
        
        echo "✅ Migraciones completadas exitosamente"
    waitFor: ['push-backend']

# Images to store in registry
images:
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$COMMIT_SHA'

# Timeout for migration process
timeout: '1800s'  # 30 minutes

# Configure logging
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_4'
