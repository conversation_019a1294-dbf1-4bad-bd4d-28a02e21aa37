#!/usr/bin/env python3
"""
Script para verificar que el despliegue se completó correctamente.
"""

import requests
import sys
import time


def check_backend_health():
    """Verificar que el backend esté funcionando."""
    backend_url = "https://rayuela-backend-lrw7xazcbq-uc.a.run.app"
    
    try:
        print(f"🔍 Verificando backend en {backend_url}/health...")
        response = requests.get(f"{backend_url}/health", timeout=10)
        
        if response.status_code == 200:
            print("✅ Backend está funcionando correctamente")
            return True
        else:
            print(f"❌ Backend respondió con código {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error al conectar con el backend: {e}")
        return False


def check_frontend_health():
    """Verificar que el frontend esté funcionando."""
    frontend_url = "https://rayuela-frontend-lrw7xazcbq-uc.a.run.app"
    
    try:
        print(f"🔍 Verificando frontend en {frontend_url}...")
        response = requests.get(frontend_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ Frontend está funcionando correctamente")
            return True
        else:
            print(f"❌ Frontend respondió con código {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error al conectar con el frontend: {e}")
        return False


def main():
    """Verificar el estado completo del despliegue."""
    print("🚀 VERIFICACIÓN DE DESPLIEGUE")
    print("=" * 40)
    
    # Esperar un momento para que los servicios se estabilicen
    print("⏳ Esperando 30 segundos para que los servicios se estabilicen...")
    time.sleep(30)
    
    backend_ok = check_backend_health()
    frontend_ok = check_frontend_health()
    
    print("\n📋 RESUMEN:")
    print(f"   Backend: {'✅ OK' if backend_ok else '❌ FALLO'}")
    print(f"   Frontend: {'✅ OK' if frontend_ok else '❌ FALLO'}")
    
    if backend_ok and frontend_ok:
        print("\n🎉 ¡DESPLIEGUE COMPLETADO EXITOSAMENTE!")
        return 0
    else:
        print("\n❌ El despliegue tiene problemas")
        return 1


if __name__ == "__main__":
    sys.exit(main())
