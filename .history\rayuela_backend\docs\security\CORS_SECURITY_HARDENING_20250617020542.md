# CORS Security Hardening Implementation

## Overview

This document describes the security hardening implemented for Cross-Origin Resource Sharing (CORS) configuration to address potential security vulnerabilities identified in security audit.

## Security Issues Addressed

### 1. Permissive CORS Origins Configuration
- **Previous Issue**: `ALLOWED_ORIGINS` defaulted to `["*"]` allowing all origins
- **Security Risk**: Enables Cross-Site Request Forgery (CSRF) and Cross-Site Scripting (XSS) attacks
- **Fix**: Implemented strict origin validation with specific allowed domains

### 2. Wildcard Methods and Headers
- **Previous Issue**: `allow_methods=["*"]` and `allow_headers=["*"]` in CORS middleware
- **Security Risk**: Unnecessarily permissive, violates principle of least privilege
- **Fix**: Restricted to specific required HTTP methods and headers

### 3. Missing Production Validation
- **Previous Issue**: No validation preventing wildcard origins in production
- **Security Risk**: Accidental deployment with insecure CORS configuration
- **Fix**: Added production environment validation

## Implementation Details

### 1. CORS Origins Configuration (`src/core/config.py`)

#### Development Configuration
```python
ALLOWED_ORIGINS: List[str] = [
    "http://localhost:3000",      # Next.js dev server
    "http://127.0.0.1:3000", 
    "http://localhost:5173",      # Vite dev server
    "http://127.0.0.1:5173"
]
```

#### Production Configuration (Environment Variables)
```bash
ALLOWED_ORIGINS=https://rayuela.com,https://app.rayuela.com,https://api.rayuela.com
```

### 2. Production Validation

Added `validate_allowed_origins()` method that enforces:
- No wildcard (`*`) origins in production
- No empty origins list in production  
- No localhost/local IP addresses in production
- Warning for HTTP origins in production (HTTPS recommended)

### 3. CORS Middleware Hardening (`src/middleware/setup.py`)

#### Allowed Methods (Restricted)
```python
allow_methods=[
    "GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD"
]
```

#### Allowed Headers (Specific to Application Needs)
```python
allow_headers=[
    "accept", "accept-encoding", "authorization", "content-type",
    "dnt", "origin", "user-agent", "x-csrftoken", "x-requested-with",
    "x-api-key", "x-tenant-id", "cache-control"
]
```

## Security Benefits

1. **CSRF Protection**: Strict origin validation prevents unauthorized cross-origin requests
2. **XSS Mitigation**: Reduces attack surface by limiting allowed origins
3. **Principle of Least Privilege**: Only necessary methods and headers are allowed
4. **Environment Safety**: Production validation prevents accidental insecure deployments
5. **Audit Trail**: Clear configuration makes security review easier

## Configuration Guidelines

### Development Environment
- Use localhost and specific development ports
- Avoid wildcard origins even in development for consistency

### Production Environment
- **NEVER** use wildcard (`*`) origins
- Use exact domain names with HTTPS
- Include all legitimate frontend domains
- Regularly audit and update allowed origins

### Example Secure Production Configuration
```bash
# Environment Variables
ENV=production
ALLOWED_ORIGINS=https://app.example.com,https://admin.example.com
ALLOWED_HOSTS=api.example.com,backend.example.com
```

## Monitoring and Maintenance

### Regular Security Reviews
1. **Quarterly Review**: Audit all allowed origins for continued legitimacy
2. **Domain Changes**: Update CORS configuration when domains change
3. **Security Scanning**: Include CORS configuration in security scans

### Error Monitoring
- Monitor application logs for CORS-related errors
- Set up alerts for configuration validation failures
- Track rejected cross-origin requests

## Testing

### Manual Testing
```bash
# Test CORS configuration
curl -H "Origin: https://malicious.com" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS \
     https://api.example.com/v1/health
```

### Automated Testing
- Include CORS security tests in CI/CD pipeline
- Test both allowed and rejected origins
- Verify production configuration validation

## Migration Notes

### Immediate Actions Required
1. Update production environment variables with specific origins
2. Test all frontend applications for CORS compatibility
3. Monitor logs for any CORS-related errors after deployment

### Breaking Changes
- Wildcard origins no longer supported in production
- Some previously accepted methods/headers may be rejected
- Applications must specify exact origins

## Compliance and Standards

This implementation aligns with:
- **OWASP Security Guidelines**: Cross-Origin Resource Sharing
- **RFC 6454**: The Web Origin Concept
- **W3C CORS Specification**: Cross-Origin Resource Sharing
- **Security Best Practices**: Principle of least privilege

## References

- [OWASP CORS Security Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Origin_Resource_Sharing_Cheat_Sheet.html)
- [MDN CORS Documentation](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS)
- [RFC 6454 - The Web Origin Concept](https://tools.ietf.org/html/rfc6454) 