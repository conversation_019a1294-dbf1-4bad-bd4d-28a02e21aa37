# 7. Pipeline de Entrenamiento

Esta sección detalla los endpoints disponibles para gestionar el entrenamiento de modelos de recomendación en Rayuela.

## Visión General

El pipeline de entrenamiento de Rayuela es un proceso asíncrono que permite entrenar modelos de recomendación personalizados para cada cuenta. El proceso incluye:

1. **Preparación de datos**: Extracción y transformación de datos de usuarios, productos e interacciones
2. **Entrenamiento del modelo**: Aplicación de algoritmos de aprendizaje automático para generar modelos de recomendación
3. **Evaluación**: Cálculo de métricas de rendimiento para evaluar la calidad del modelo
4. **Despliegue**: Puesta en producción del modelo entrenado para generar recomendaciones

## Iniciar Entrenamiento (`POST /pipeline/train`)

Inicia un trabajo de entrenamiento para generar un nuevo modelo de recomendación. Este proceso es asíncrono y puede tomar varios minutos dependiendo del volumen de datos.

### Request Body (opcional)

```json
{
  "model_type": "hybrid",
  "hyperparameters": {
    "learning_rate": 0.01,
    "epochs": 20,
    "embedding_dim": 64,
    "regularization": 0.001
  },
  "force": false
}
```

- **`model_type`** (opcional, default: "hybrid"): Tipo de modelo a entrenar ("collaborative", "content", "hybrid")
- **`hyperparameters`** (opcional): Parámetros específicos para el algoritmo de entrenamiento
- **`force`** (opcional, default: false): Forzar el entrenamiento incluso si se ha entrenado recientemente

### Response Body

```json
{
  "message": "Training job started successfully",
  "job_id": 123,
  "task_id": "abc123def456",
  "status": "pending",
  "estimated_time": 300
}
```

- **`job_id`**: ID del trabajo de entrenamiento
- **`task_id`**: ID de la tarea asíncrona (Celery)
- **`status`**: Estado inicial del trabajo
- **`estimated_time`**: Tiempo estimado de finalización en segundos

### Validaciones

- **Límite de frecuencia**: Según el plan de suscripción, puede haber restricciones sobre la frecuencia de entrenamiento
- **Volumen de datos**: Se requiere un mínimo de datos para entrenar un modelo efectivo
- **Permisos**: El usuario debe tener permisos para iniciar trabajos de entrenamiento

### Ejemplo

```bash
curl -X POST "https://api.rayuela.ai/api/v1/pipeline/train" \
     -H "X-API-Key: tu_api_key" \
     -H "Content-Type: application/json" \
     -d '{
       "model_type": "hybrid",
       "hyperparameters": {
         "learning_rate": 0.01,
         "epochs": 20
       }
     }'
```

## Consultar Estado del Trabajo (`GET /pipeline/jobs/{job_id}/status`)

Obtiene el estado actual de un trabajo de entrenamiento.

### Parámetros de Ruta

- **`job_id`** (obligatorio): ID del trabajo de entrenamiento

### Response Body

```json
{
  "job_id": 123,
  "status": "completed",
  "created_at": "2023-10-26T10:00:00Z",
  "started_at": "2023-10-26T10:01:00Z",
  "completed_at": "2023-10-26T10:10:00Z",
  "error_message": null,
  "progress_percentage": 100.0,
  "metrics": {
    "precision": 0.85,
    "recall": 0.78,
    "f1_score": 0.81,
    "ndcg": 0.92
  },
  "model_info": {
    "model_type": "hybrid",
    "artifact_name": "model_123_20231026",
    "artifact_version": "v1",
    "data_points": 15000
  }
}
```

- **`status`**: Estado del trabajo ("pending", "running", "completed", "failed")
- **`created_at`**: Fecha y hora de creación del trabajo
- **`started_at`**: Fecha y hora de inicio del trabajo
- **`completed_at`**: Fecha y hora de finalización del trabajo
- **`error_message`**: Mensaje de error si el trabajo falló
- **`progress_percentage`**: Porcentaje de progreso del trabajo
- **`metrics`**: Métricas de rendimiento del modelo entrenado
- **`model_info`**: Información sobre el modelo entrenado

### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/pipeline/jobs/123/status" \
     -H "X-API-Key: tu_api_key"
```

## Listar Modelos Entrenados (`GET /pipeline/models`)

Obtiene una lista de todos los modelos entrenados para la cuenta actual.

### Parámetros de Consulta

- **`skip`** (opcional, default: 0): Número de elementos a saltar
- **`limit`** (opcional, default: 10): Número máximo de elementos a devolver

### Response Body

```json
{
  "items": [
    {
      "model_id": "model_123_20231026",
      "version": "v1",
      "model_type": "hybrid",
      "created_at": "2023-10-26T10:10:00Z",
      "is_active": true,
      "metrics": {
        "precision": 0.85,
        "recall": 0.78,
        "f1_score": 0.81,
        "ndcg": 0.92
      },
      "data_points": 15000
    },
    {
      "model_id": "model_122_20231020",
      "version": "v1",
      "model_type": "collaborative",
      "created_at": "2023-10-20T15:30:00Z",
      "is_active": false,
      "metrics": {
        "precision": 0.82,
        "recall": 0.75,
        "f1_score": 0.78,
        "ndcg": 0.89
      },
      "data_points": 14500
    }
  ],
  "total": 2,
  "skip": 0,
  "limit": 10
}
```

### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/pipeline/models?limit=5" \
     -H "X-API-Key: tu_api_key"
```

## Obtener Métricas de un Modelo (`GET /pipeline/models/{model_id}/metrics`)

Obtiene métricas detalladas de rendimiento para un modelo específico.

### Parámetros de Ruta

- **`model_id`** (obligatorio): ID del modelo

### Response Body

```json
{
  "model_id": "model_123_20231026",
  "version": "v1",
  "metrics": {
    "overall": {
      "precision": 0.85,
      "recall": 0.78,
      "f1_score": 0.81,
      "ndcg": 0.92,
      "map": 0.88,
      "coverage": 0.95
    },
    "by_category": {
      "electronics": {
        "precision": 0.87,
        "recall": 0.80
      },
      "clothing": {
        "precision": 0.83,
        "recall": 0.76
      }
    },
    "by_user_segment": {
      "new_users": {
        "precision": 0.80,
        "recall": 0.72
      },
      "active_users": {
        "precision": 0.89,
        "recall": 0.82
      }
    }
  },
  "training_info": {
    "duration_seconds": 540,
    "iterations": 20,
    "convergence": true,
    "data_points": 15000
  }
}
```

### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/pipeline/models/model_123_20231026/metrics" \
     -H "X-API-Key: tu_api_key"
```

## Rollback de Modelo (`POST /pipeline/rollback/{artifact_version}`)

Revierte a una versión anterior del modelo. Esta operación es sensible y debe usarse con precaución.

### Parámetros de Ruta

- **`artifact_version`** (obligatorio): Versión del artefacto a la que se desea revertir

### Request Body

```json
{
  "reason": "Performance degradation in new model",
  "force": false
}
```

- **`reason`** (opcional): Motivo del rollback
- **`force`** (opcional, default: false): Forzar el rollback incluso si hay advertencias

### Response Body

```json
{
  "message": "Rollback successful",
  "previous_version": "v2",
  "current_version": "v1",
  "status": "completed"
}
```

### Ejemplo

```bash
curl -X POST "https://api.rayuela.ai/api/v1/pipeline/rollback/v1" \
     -H "X-API-Key: tu_api_key" \
     -H "Content-Type: application/json" \
     -d '{
       "reason": "Performance degradation in new model"
     }'
```

## Entrenamiento para Administradores (`POST /pipeline/train/{account_id}`)

> **Nota**: Este endpoint es solo para administradores del sistema.

Inicia un trabajo de entrenamiento para una cuenta específica.

### Parámetros de Ruta

- **`account_id`** (obligatorio): ID de la cuenta para la que se desea entrenar un modelo

### Request Body

Similar al endpoint `POST /pipeline/train`.

### Ejemplo

```bash
curl -X POST "https://api.rayuela.ai/api/v1/pipeline/train/456" \
     -H "Authorization: Bearer tu_token_jwt" \
     -H "Content-Type: application/json" \
     -d '{
       "model_type": "hybrid"
     }'
```

## Mejores Prácticas

1. **Frecuencia de entrenamiento**: Entrene modelos regularmente para mantenerlos actualizados, pero no con demasiada frecuencia para evitar sobrecargar el sistema.

2. **Monitoreo de métricas**: Verifique las métricas de rendimiento después de cada entrenamiento para asegurarse de que el modelo está mejorando.

3. **Rollback con precaución**: Utilice la función de rollback solo cuando sea necesario y después de evaluar cuidadosamente el impacto.

4. **Volumen de datos**: Asegúrese de tener suficientes datos para entrenar un modelo efectivo. Se recomienda al menos 1000 interacciones.

5. **Hiperparámetros**: Experimente con diferentes hiperparámetros para encontrar la configuración óptima para sus datos.
