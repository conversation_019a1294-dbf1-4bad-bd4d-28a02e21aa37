// src/lib/useUsageSummary.ts
import useSWR from 'swr';
import { getUsageSummary } from '@/lib/api';
import { useAuth } from '@/lib/auth';
import { GetUsageSummaryApiV1UsageSummaryGet200 } from '@/lib/generated/rayuelaAPI';

// Tipo temporal hasta que se actualice el cliente generado
export interface UsageSummaryResponse {
  subscription: {
    plan: string;
    isActive: boolean;
    expiresAt?: string | null;
  };
  apiCalls: {
    used: number;
    limit: number;
    percentage: number;
    resetDate?: string | null;
  };
  storage: {
    usedBytes: number;
    limitBytes: number;
    percentage: number;
    lastMeasured?: string | null;
    source?: string | null;
  };
  training: {
    lastTrainingDate?: string | null;
    frequencyLimit: string;
    nextAvailable?: string | null;
    canTrainNow: boolean;
  };
  planLimits: {
    apiCalls: number;
    storageBytes: number;
    storageMb: number;
    storageGb: number;
    maxRequestsPerMinute: number;
    maxConcurrentRequests: number;
    trainingFrequency: string;
    trainingDataLimit: number;
    trainingDataLimitFormatted: string;
    maxItems: number;
    maxItemsFormatted: string;
    maxUsers: number;
    maxUsersFormatted: string;
    recommendationCacheTtl: number;
    availableModels?: string[];
  };
  allPlans: Record<string, any>;
  billingPortalUrl?: string | null;
}

export interface UsageSummaryState {
  usageData: UsageSummaryResponse | undefined;
  error: Error | string | null;
  isLoading: boolean;
  mutate: () => void;
}

export function useUsageSummary(): UsageSummaryState & {
  // Helper functions for common usage calculations
  getApiCallsUsed: () => number;
  getApiCallsRemaining: () => number;
  getApiCallsLimit: () => number;
  getStorageUsed: () => number;
  getStorageRemaining: () => number;
  getStorageLimit: () => number;
  hasUsageActivity: () => boolean;
  getApiCallsPercentage: () => number;
  getStoragePercentage: () => number;
  canTrainNow: () => boolean;
  getNextTrainingDate: () => Date | null;
  getLastStorageMeasurement: () => string | null;
  getNextApiCallsReset: () => Date | null;
  isApiCallsLimitReached: () => boolean;
  isStorageLimitReached: () => boolean;
  getStorageUsedFormatted: () => string;
  getStorageLimitFormatted: () => string;
  getApiCallsUsedFormatted: () => string;
  getApiCallsLimitFormatted: () => string;
  getAvailableModels: () => string[];
  getMaxRequestsPerMinute: () => number;
} {
  const { token, apiKey } = useAuth();

  const {
    data,
    error,
    isLoading,
    mutate,
  } = useSWR<UsageSummaryResponse>(
    token && apiKey ? ['usage-summary', token, apiKey] : null,
    async () => {
      const response = await getUsageSummary();
      return response as UsageSummaryResponse;
    },
    {
      refreshInterval: 30000, // Refresh every 30 seconds
      revalidateOnFocus: true,
    }
  );

  // Helper functions using actual UsageSummaryResponse structure
  const getApiCallsPercentage = (): number => {
    if (!data) return 0;
    return data.apiCalls?.percentage || 0;
  };

  const getStoragePercentage = (): number => {
    if (!data) return 0;
    return data.storage?.percentage || 0;
  };

  const canTrainNow = (): boolean => {
    if (!data) return false;
    return data.training?.canTrainNow || false;
  };

  const getNextTrainingDate = (): Date | null => {
    if (!data || !data.training?.nextAvailable) return null;
    return new Date(data.training.nextAvailable);
  };

  const getLastStorageMeasurement = (): string | null => {
    if (!data) return null;
    return data.storage?.lastMeasured || null;
  };

  const getNextApiCallsReset = (): Date | null => {
    if (!data || !data.apiCalls?.resetDate) return null;
    return new Date(data.apiCalls.resetDate);
  };

  const isApiCallsLimitReached = (): boolean => {
    if (!data) return false;
    return getApiCallsPercentage() >= 100;
  };

  const isStorageLimitReached = (): boolean => {
    if (!data) return false;
    return getStoragePercentage() >= 100;
  };

  const getStorageUsedFormatted = (): string => {
    if (!data) return '0 B';
    const bytes = data.storage?.usedBytes || 0;
    
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStorageLimitFormatted = (): string => {
    if (!data) return '0 B';
    const bytes = data.storage?.limitBytes || 0;
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getApiCallsUsedFormatted = (): string => {
    if (!data) return '0';
    return (data.apiCalls?.used || 0).toLocaleString();
  };

  const getApiCallsLimitFormatted = (): string => {
    if (!data) return '0';
    return (data.apiCalls?.limit || 0).toLocaleString();
  };

  const getAvailableModels = (): string[] => {
    if (!data) return [];
    return data.planLimits?.availableModels || [];
  };

  const getMaxRequestsPerMinute = (): number => {
    if (!data) return 0;
    return data.planLimits?.maxRequestsPerMinute || 0;
  };

  return {
    usageData: data,
    error,
    isLoading,
    mutate,
    // Helper functions for common usage calculations
    getApiCallsUsed: () => data?.apiCalls?.used || 0,
    getApiCallsLimit: () => data?.apiCalls?.limit || 0,
    getApiCallsRemaining: () => {
      const used = data?.apiCalls?.used || 0;
      const limit = data?.apiCalls?.limit || 0;
      return Math.max(0, limit - used);
    },
    getStorageUsed: () => data?.storage?.usedBytes || 0,
    getStorageLimit: () => data?.storage?.limitBytes || 0,
    getStorageRemaining: () => {
      const used = data?.storage?.usedBytes || 0;
      const limit = data?.storage?.limitBytes || 0;
      return Math.max(0, limit - used);
    },
    // Check if user has made API calls
    hasUsageActivity: () => (data?.apiCalls?.used || 0) > 0,
    getApiCallsPercentage,
    getStoragePercentage,
    canTrainNow,
    getNextTrainingDate,
    getLastStorageMeasurement,
    getNextApiCallsReset,
    isApiCallsLimitReached,
    isStorageLimitReached,
    getStorageUsedFormatted,
    getStorageLimitFormatted,
    getApiCallsUsedFormatted,
    getApiCallsLimitFormatted,
    getAvailableModels,
    getMaxRequestsPerMinute,
  };
}
