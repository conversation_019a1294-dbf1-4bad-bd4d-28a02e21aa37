from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    ForeignKey,
    Text,
    UniqueConstraint,
    Index,
    func,
    JSON,
    PrimaryKeyConstraint,
    Identity,
)
from sqlalchemy.orm import relationship
from src.db.base import Base
from .mixins import TenantMixin, ACCOUNT_ID_FK, get_tenant_table_args


class AuditLog(Base, TenantMixin):
    __tablename__ = "audit_logs"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    id = Column(Integer, Identity(), primary_key=True)
    action = Column(String, nullable=False)
    entity_type = Column(String, nullable=False)
    entity_id = Column(Integer, nullable=False)
    changes = Column(JSON)
    performed_by = Column(String, nullable=False)
    created_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now())
    details = Column(Text)

    __table_args__ = get_tenant_table_args(
        PrimaryKeyConstraint("account_id", "id"),
        Index("idx_audit_account_timestamp", "account_id", "created_at")
    )

    account = relationship("Account", back_populates="audit_log")
