from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from .end_user import EndUserCreate
from .product import ProductCreate
from .interaction import InteractionCreate


class BatchIngestionRequest(BaseModel):
    """Esquema para la carga masiva de datos"""

    users: Optional[List[EndUserCreate]] = Field(
        None, description="Lista de usuarios a cargar"
    )
    products: Optional[List[ProductCreate]] = Field(
        None, description="Lista de productos a cargar"
    )
    interactions: Optional[List[InteractionCreate]] = Field(
        None, description="Lista de interacciones a cargar"
    )


class BatchIngestionResponse(BaseModel):
    """Esquema para la respuesta de la carga masiva"""

    message: str
    status: str
    job_id: int
    task_id: Optional[str] = None
    total_users: int
    total_products: int
    total_interactions: int
