"""Add system_user_id column to audit_logs and FK to system_users

Revision ID: 20250704_120000
Revises: 20250701_020000
Create Date: 2025-07-04 12:00:00
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = "20250704_120000"
down_revision: Union[str, None] = "20250701_020000"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def _column_exists(table_name: str, column_name: str) -> bool:
    connection = op.get_bind()
    result = connection.execute(
        text(
            """
        SELECT column_name FROM information_schema.columns
        WHERE table_name = :table_name
          AND column_name = :column_name
          AND table_schema = 'public'
        """
        ),
        {"table_name": table_name, "column_name": column_name},
    )
    return result.fetchone() is not None


def _constraint_exists(constraint_name: str, table_name: str) -> bool:
    """Helper function to check if a constraint exists"""
    connection = op.get_bind()
    result = connection.execute(
        text(
            """
        SELECT constraint_name FROM information_schema.table_constraints
        WHERE table_name = :table_name
          AND constraint_name = :constraint_name
          AND table_schema = 'public'
        """
        ),
        {"table_name": table_name, "constraint_name": constraint_name},
    )
    return result.fetchone() is not None


def _table_exists(table_name: str) -> bool:
    """Check if a table exists."""
    connection = op.get_bind()
    result = connection.execute(
        text("""
        SELECT 1 FROM information_schema.tables
        WHERE table_name = :table_name
        """),
        {"table_name": table_name}
    )
    return result.fetchone() is not None


def upgrade() -> None:
    """Add system_user_id column to audit_logs (simplified approach)."""

    print("Adding system_user_id column to audit_logs...")

    # Step 1: Add system_user_id column to audit_logs
    if not _column_exists("audit_logs", "system_user_id"):
        print("  Adding system_user_id column...")
        op.add_column("audit_logs", sa.Column("system_user_id", sa.Integer(), nullable=True))
        print("  Column added successfully!")
    else:
        print("  system_user_id column already exists, skipping...")

    print("Migration completed successfully!")


def downgrade() -> None:
    """Revert to original table structure."""

    print("Reverting to original table structure...")

    # Step 1: Drop foreign key constraint from audit_logs
    if _constraint_exists("fk_audit_logs_system_user", "audit_logs"):
        op.drop_constraint("fk_audit_logs_system_user", "audit_logs", type_="foreignkey")

    # Step 2: Drop system_user_id column from audit_logs
    if _column_exists("audit_logs", "system_user_id"):
        op.drop_column("audit_logs", "system_user_id")

    # Step 3: Drop and recreate tables with original structure
    op.drop_table('system_user_roles')
    op.drop_table('system_users')
    op.drop_table('roles')

    # Recreate roles with original structure (role_id as primary key)
    op.create_table('roles',
        sa.Column('role_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('role_id'),
        sa.UniqueConstraint('account_id', 'name', name='uq_role_account_name')
    )

    # Recreate system_users with original structure (user_id as primary key)
    op.create_table('system_users',
        sa.Column('user_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('email', sa.String(length=255), nullable=False),
        sa.Column('password_hash', sa.String(length=255), nullable=False),
        sa.Column('first_name', sa.String(length=100), nullable=True),
        sa.Column('last_name', sa.String(length=100), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('user_id'),
        sa.UniqueConstraint('account_id', 'email', name='uq_system_user_account_email')
    )

    # Recreate system_user_roles with original structure
    op.create_table('system_user_roles',
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('role_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['system_users.user_id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['role_id'], ['roles.role_id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('user_id', 'role_id')
    )

    print("Reverted to original table structure successfully!")