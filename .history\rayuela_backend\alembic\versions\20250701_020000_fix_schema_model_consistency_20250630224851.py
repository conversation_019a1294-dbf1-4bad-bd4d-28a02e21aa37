"""Fix schema to match SQLAlchemy models

Revision ID: 20250701_020000
Revises: 20250701_010001
Create Date: 2025-07-01 02:00:00.000000

This migration fixes the database schema to match the SQLAlchemy models exactly.
Since there's no data yet, we can safely restructure the tables.

Key changes:
1. EndUser: Change from end_user_id (single PK) to user_id + account_id (composite PK)
2. Interaction: Change from interaction_id (single PK) to account_id + id (composite PK), end_user_id -> user_id
3. Search: Change from search_id (single PK) to account_id + id (composite PK), end_user_id -> user_id  
4. AuditLog: Change from audit_id (single PK) to account_id + id (composite PK)
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy import text
from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = "20250701_020000"
down_revision: Union[str, None] = "20250701_010001"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def _drop_constraint_if_exists(constraint_name: str, table_name: str, constraint_type: str):
    """Helper function to drop constraint only if it exists"""
    connection = op.get_bind()

    # Check if constraint exists
    result = connection.execute(text("""
        SELECT constraint_name
        FROM information_schema.table_constraints
        WHERE table_name = :table_name
        AND constraint_name = :constraint_name
        AND table_schema = 'public'
    """), {"table_name": table_name, "constraint_name": constraint_name})

    if result.fetchone():
        print(f"  ✅ Dropping existing constraint: {constraint_name}")
        op.drop_constraint(constraint_name, table_name, type_=constraint_type)
    else:
        print(f"  ⚠️ Constraint {constraint_name} does not exist, skipping...")


def upgrade() -> None:
    """Apply schema fixes to match SQLAlchemy models."""
    
    print("Fixing database schema to match SQLAlchemy models...")
    
    # 1. Fix EndUser table
    print("1. Fixing EndUser table structure...")

    # Drop existing constraints and indexes
    _drop_constraint_if_exists('uq_end_user_account_external_id', 'end_users', 'unique')
    _drop_constraint_if_exists('end_users_pkey', 'end_users', 'primary')
    
    # Rename end_user_id to user_id
    op.alter_column('end_users', 'end_user_id', new_column_name='user_id')
    
    # Create composite primary key (user_id, account_id)
    op.create_primary_key('end_users_pkey', 'end_users', ['user_id', 'account_id'])
    
    # Recreate unique constraint with correct name
    op.create_unique_constraint('uq_end_user_external_id', 'end_users', ['account_id', 'external_id'])
    
    
    # 2. Fix Interaction table
    print("2. Fixing Interaction table structure...")
    
    # Drop all foreign key constraints first
    op.drop_constraint('interactions_end_user_id_fkey', 'interactions', type_='foreignkey')
    op.drop_constraint('interactions_product_id_fkey', 'interactions', type_='foreignkey')
    op.drop_constraint('interactions_pkey', 'interactions', type_='primary')
    
    # Rename columns to match model
    op.alter_column('interactions', 'interaction_id', new_column_name='id')
    op.alter_column('interactions', 'end_user_id', new_column_name='user_id')
    op.alter_column('interactions', 'rating', new_column_name='value')
    op.alter_column('interactions', 'context', new_column_name='recommendation_metadata')
    
    # Remove extra columns that don't exist in model
    op.drop_column('interactions', 'session_id')
    op.drop_column('interactions', 'created_at')
    op.drop_column('interactions', 'updated_at')
    
    # Create composite primary key (account_id, id)
    op.create_primary_key('interactions_pkey', 'interactions', ['account_id', 'id'])
    
    # Recreate foreign key constraints with composite keys
    op.create_foreign_key(
        'fk_interaction_user', 'interactions', 'end_users',
        ['account_id', 'user_id'], ['account_id', 'user_id'],
        ondelete='CASCADE'
    )
    op.create_foreign_key(
        'fk_interaction_product', 'interactions', 'products', 
        ['account_id', 'product_id'], ['account_id', 'product_id'],
        ondelete='CASCADE'
    )
    
    
    # 3. Fix Search table
    print("3. Fixing Search table structure...")
    
    # Drop constraints
    op.drop_constraint('searches_end_user_id_fkey', 'searches', type_='foreignkey')
    op.drop_constraint('searches_pkey', 'searches', type_='primary')
    
    # Rename columns
    op.alter_column('searches', 'search_id', new_column_name='id')
    op.alter_column('searches', 'end_user_id', new_column_name='user_id')
    
    # Remove extra columns that don't exist in model
    op.drop_column('searches', 'filters')
    op.drop_column('searches', 'results_count')
    op.drop_column('searches', 'session_id')
    op.drop_column('searches', 'created_at')
    op.drop_column('searches', 'updated_at')
    
    # Create composite primary key (account_id, id)
    op.create_primary_key('searches_pkey', 'searches', ['account_id', 'id'])
    
    # Recreate foreign key constraint with composite key
    op.create_foreign_key(
        'fk_search_user', 'searches', 'end_users',
        ['account_id', 'user_id'], ['account_id', 'user_id'],
        ondelete='CASCADE'
    )


    # 4. Fix AuditLog table
    print("4. Fixing AuditLog table structure...")

    # Drop constraints
    op.drop_constraint('audit_logs_user_id_fkey', 'audit_logs', type_='foreignkey')
    op.drop_constraint('audit_logs_pkey', 'audit_logs', type_='primary')

    # Rename columns to match model
    op.alter_column('audit_logs', 'audit_id', new_column_name='id')
    op.alter_column('audit_logs', 'old_values', new_column_name='changes')
    op.alter_column('audit_logs', 'timestamp', new_column_name='created_at')

    # Remove columns that don't exist in the model
    op.drop_column('audit_logs', 'user_id')
    op.drop_column('audit_logs', 'new_values')
    op.drop_column('audit_logs', 'ip_address')
    op.drop_column('audit_logs', 'user_agent')

    # Create composite primary key (account_id, id)
    op.create_primary_key('audit_logs_pkey', 'audit_logs', ['account_id', 'id'])

    print("Schema consistency fixes completed successfully!")


def downgrade() -> None:
    """Revert schema fixes."""
    
    # This is a complex downgrade that would need to reverse all the changes
    # For now, we'll raise an exception to prevent accidental downgrades
    raise NotImplementedError(
        "Downgrade not implemented for schema consistency fixes. "
        "This migration makes structural changes that are difficult to reverse safely."
    )
