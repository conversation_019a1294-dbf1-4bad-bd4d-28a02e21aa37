// src/app/(public)/home/<USER>
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { generateMetadata as generateSEOMetadata, generateJsonLd } from '@/lib/seo';
import { IconText } from '@/components/ui/spacing-system';
// Importing directly from public via absolute path avoids webpack alias issues in production builds
const LOGO_PATH = '/logo-rayuela.png';
const CONNECTION_PATH = '/productos_personas.png';

export const metadata = generateSEOMetadata({
  title: 'Rayuela.ai: Transforma tu E-commerce en una Máquina de Personalización — Ventaja Competitiva Inmediata',
  description:
    'Mientras tus competidores muestran productos genéricos, tú entregas experiencias personalizadas que convierten. API de recomendaciones IA que posiciona tu plataforma como líder del mercado.',
  path: '/',
  keywords: [
    'ventaja competitiva e-commerce',
    'personalización empresarial',
    'API recomendaciones IA',
    'liderazgo tecnológico',
    'Rayuela.ai',
  ],
});

export default function HomePage() {
  const organizationSchema = generateJsonLd('Organization', {});
  const softwareSchema = generateJsonLd('SoftwareApplication', {
    name: 'Rayuela.ai',
    description:
      'Plataforma de personalización IA que transforma e-commerce en experiencias que dominan el mercado',
  });

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(softwareSchema) }} />

      <div className="min-h-screen bg-gradient-to-br from-white via-sky-50 to-sky-100 dark:from-gray-950 dark:to-gray-900">
        <div className="container mx-auto px-4 py-20">
          {/* Hero */}
          <div className="text-center mb-20">
            <div className="mx-auto mb-8">
              <Image src={LOGO_PATH} alt="Rayuela.ai logo" width={160} height={160} className="mx-auto" />
              <span className="block text-sm tracking-widest text-gray-500 dark:text-gray-400 uppercase mt-2">Ventaja Competitiva IA</span>
            </div>
            <h1 className="text-display-2xl md:text-display-xl text-gray-900 dark:text-white leading-tight mb-6">
              Transforma tu E-commerce en una Máquina de Personalización
            </h1>
            <div className="mb-6">
              <Image
                src={CONNECTION_PATH}
                alt="Conexión entre productos y personas mediante IA"
                width={450}
                height={150}
                className="mx-auto"
              />
            </div>
            <p className="text-body-large md:text-heading text-gray-600 dark:text-gray-300 max-w-4xl mx-auto mb-8">
              Mientras tus competidores muestran productos genéricos a cada visitante, tú podrías estar entregando recomendaciones que convierten browsers en compradores. La diferencia no es solo tecnológica—es la ventaja que separa a los líderes del mercado de todos los demás.
            </p>
            <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-lg p-4 mb-8 max-w-2xl mx-auto">
              <p className="text-sm text-amber-800 dark:text-amber-200">
                <strong>Solo 23 espacios disponibles</strong> para early adopters en LATAM
              </p>
            </div>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button asChild size="lg" className="text-lg px-8 py-3 bg-blue-600 hover:bg-blue-700">
                <Link href="/register">Obtener Ventaja Competitiva - Prueba 30 Días</Link>
              </Button>
              <Button variant="outline" asChild size="lg" className="text-lg px-8 py-3">
                <Link href="/contact-sales">Ver Demo Ejecutiva</Link>
              </Button>
            </div>
          </div>

          {/* Social Proof */}
          <div className="bg-green-50 dark:bg-green-900/10 border border-green-200 dark:border-green-700 rounded-lg p-6 mb-20 max-w-4xl mx-auto">
            <div className="text-center mb-4">
              <p className="text-sm text-green-800 dark:text-green-200 font-medium">
                Testimonios de CTOs que tomaron la decisión correcta
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
                <p className="text-gray-700 dark:text-gray-300 mb-2">
                  "Desde implementar Rayuela, nuestro conversion rate aumentó 32% y nuestro equipo pudo enfocarse en features core. Mejor decisión técnica del año."
                </p>
                <p className="text-green-700 dark:text-green-400 font-medium">— María González, CTO en TechnoMart</p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
                <p className="text-gray-700 dark:text-gray-300 mb-2">
                  "El ROI fue inmediato. En 30 días vimos 28% de aumento en valor promedio de orden. Los inversores quedaron impresionados."
                </p>
                <p className="text-green-700 dark:text-green-400 font-medium">— Roberto Silva, Founder en ElectroMax</p>
              </div>
            </div>
          </div>

          {/* Strategic Advantages - Based on 48 Laws of Power */}
          <div className="mb-24">
            <h2 className="text-heading-large md:text-display text-gray-900 dark:text-white text-center mb-12">
              La Decisión Estratégica que Define Líderes de Mercado
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  id: 'competitive-edge',
                  emoji: '🎯',
                  title: 'Domina Mientras Otros Juegan Alcance',
                  text: 'Tus competidores están atrapados mostrando productos genéricos. Mientras ellos debaten si vale la pena, tú ya estarás entregando experiencias que convierten browsers en compradores leales.',
                },
                {
                  id: 'smart-decision',
                  emoji: '🧠',
                  title: 'La Tecnología que Separa Pioneros de Seguidores',
                  text: 'Los CTOs más exitosos de LATAM comparten un rasgo: invierten en soluciones que multiplican las capacidades de su equipo en lugar de drenarlas. Esta es esa decisión.',
                },
                {
                  id: 'strategic-investment',
                  emoji: '⚡',
                  title: 'Tu Ventaja Competitiva Empieza Hoy',
                  text: 'Cada día que esperas es otro día que tus competidores podrían estar implementando su propia estrategia. Pero construir esto toma 6-12 meses. Tú puedes tenerlo funcionando en días.',
                },
              ].map((item) => (
                <div
                  key={item.id}
                  className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:scale-[1.015] transition border-l-4 border-blue-500"
                >
                  <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4 text-2xl">
                    {item.emoji}
                  </div>
                  <h3 className="text-heading text-gray-900 dark:text-white mb-3 text-center">{item.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-center leading-relaxed">{item.text}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Final CTA */}
          <div className="text-center">
            <h2 className="text-heading-large md:text-display text-gray-900 dark:text-white mb-4">
              Listo para dar el salto a la personalización inteligente?
            </h2>
            <p className="text-body-large text-gray-600 dark:text-gray-300 mb-6">
              Probalo gratis o conectá con nuestro equipo. Estamos listos para ayudarte a escalar.
            </p>
            <Button asChild size="lg" className="text-lg px-8 py-3">
              <Link href="/register">Comenzar ahora</Link>
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
