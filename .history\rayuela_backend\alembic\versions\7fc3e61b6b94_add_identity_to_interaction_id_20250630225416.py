"""add_identity_to_interaction_id

Revision ID: 7fc3e61b6b94
Revises: 1298e9a0afec
Create Date: 2025-06-30 22:54:01.265022

This migration adds Identity() to the interactions.id column to make it auto-incrementable.
This is crucial for high-volume tables like interactions where manual ID generation is error-prone.

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7fc3e61b6b94'
down_revision: Union[str, None] = '1298e9a0afec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
