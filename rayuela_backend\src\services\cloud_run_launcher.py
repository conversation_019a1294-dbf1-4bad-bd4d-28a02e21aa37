import os
import subprocess
import json
import logging
from typing import Dict, Any


def _build_gcloud_job_create_cmd(
    job_name: str,
    account_id: int,
    job_id: int,
    parameters: Dict[str, Any],
) -> list[str]:
    """Genera el comando `gcloud run jobs create` con los parámetros adecuados.

    Se construye dinámicamente para permitir flexibilidad a través de variables
    de entorno sin requerir cambios de código.
    """
    project_id = os.getenv("PROJECT_ID") or os.getenv("GCP_PROJECT") or ""
    region = os.getenv("CLOUD_RUN_REGION", "us-central1")
    build_id = os.getenv("BUILD_ID", "latest")

    # Imagen del backend que incluye todo el código necesario
    image = os.getenv(
        "TRAINING_JOB_IMAGE",
        f"{region}-docker.pkg.dev/{project_id}/rayuela-repo/rayuela-backend:{build_id}",
    )

    # Recursos y configuraciones
    timeout = os.getenv("TRAINING_JOB_TIMEOUT", "3600")  # 1 hora por defecto
    memory = os.getenv("TRAINING_JOB_MEMORY", "8Gi")
    cpu = os.getenv("TRAINING_JOB_CPU", "4")
    vpc_connector = os.getenv("CLOUD_RUN_VPC_CONNECTOR", "rayuela-vpc-connector")
    service_account = os.getenv(
        "CLOUD_RUN_SA", f"rayuela-backend-sa@{project_id}.iam.gserviceaccount.com"
    )

    # Variables de entorno que el contenedor conocerá
    env_vars = {
        "ENV": os.getenv("ENV", "production"),
        "PYTHONPATH": "/app/src",
        "WORKER_TYPE": "training",
        # Pasamos los parámetros serializados para disponibilidad dentro del job
        "TRAINING_PARAMS_JSON": json.dumps(parameters or {}),
    }

    env_vars_str = ",".join(f"{k}={v}" for k, v in env_vars.items())

    # Comando y argumentos que el contenedor ejecutará
    command = "python"
    args = [
        "-m",
        "src.workers.celery_tasks",
        "train_model_for_job",
        "--account_id",
        str(account_id),
        "--job_id",
        str(job_id),
    ]

    base_cmd = [
        "gcloud",
        "run",
        "jobs",
        "create",
        job_name,
        f"--image={image}",
        f"--region={region}",
        f"--task-timeout={timeout}",
        f"--memory={memory}",
        f"--cpu={cpu}",
        "--max-retries=0",
        "--parallelism=1",
        f"--set-env-vars={env_vars_str}",
        f"--vpc-connector={vpc_connector}",
        f"--service-account={service_account}",
        f"--command={command}",
        "--args=" + ",".join(args),
    ]

    return base_cmd


def _build_gcloud_job_execute_cmd(job_name: str) -> list[str]:
    """Comando para la ejecución del Job recién creado."""
    region = os.getenv("CLOUD_RUN_REGION", "us-central1")
    return [
        "gcloud",
        "run",
        "jobs",
        "execute",
        job_name,
        f"--region={region}",
        "--wait",
    ]


def launch_training_job(
    account_id: int,
    job_id: int,
    parameters: Dict[str, Any] | None = None,
    dry_run: bool = False,
) -> str:
    """Lanza un Cloud Run Job para entrenar modelos.

    Args:
        account_id: ID de la cuenta propietaria del entrenamiento.
        job_id: ID del registro de TrainingJob en la base de datos.
        parameters: Parámetros de entrenamiento recibidos desde el endpoint.
        dry_run: Si es *True*, no ejecuta los comandos; solo los devuelve en logs.

    Returns:
        Nombre del Cloud Run Job creado. Se reutiliza como identificador de *task_id*.
    """
    job_name = f"rayuela-training-job-{job_id}"

    create_cmd = _build_gcloud_job_create_cmd(job_name, account_id, job_id, parameters or {})
    execute_cmd = _build_gcloud_job_execute_cmd(job_name)

    logging.info("[CloudRunJob] Ejecutando creación de Job: %s", " ".join(create_cmd))

    if dry_run:
        logging.info("[CloudRunJob] Modo dry-run activado. No se lanzarán comandos.")
        return job_name

    try:
        subprocess.run(create_cmd, check=True)
        logging.info("[CloudRunJob] Job creado exitosamente: %s", job_name)
    except subprocess.CalledProcessError as exc:
        # Si el job ya existe, continuamos con la ejecución directa
        if "already exists" in str(exc):
            logging.warning("[CloudRunJob] El Job ya existía: %s", job_name)
        else:
            logging.error("[CloudRunJob] Error creando el Job: %s", exc)
            raise

    try:
        subprocess.run(execute_cmd, check=True)
        logging.info("[CloudRunJob] Job ejecutado exitosamente: %s", job_name)
    except subprocess.CalledProcessError as exc:
        logging.error("[CloudRunJob] Error ejecutando el Job: %s", exc)
        raise

    return job_name 