from typing import Sequence, Union
# Alembic helpers
from alembic import op

"""apply_partitioning_to_high_volume_tables

Revision ID: 20250701_010001
Revises: 20250625_130000
Create Date: 2025-07-01 01:00:01

This migration converts high-volume tenant-scoped tables to be
range-partitioned by account_id, aligning the database schema
with the documented and ORM-level partitioning strategy.

IMPORTANT:
    • This migration assumes the database is running PostgreSQL ≥14.
    • Existing data volume should be evaluated before applying because the
      ALTER TABLE … PARTITION BY command will rewrite each target table.
      Run during a low-traffic maintenance window.
"""

revision: str = "20250701_010001"
down_revision: Union[str, None] = "20250625_130000"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

PARTITION_SIZE = 100_000  # Keep in sync with src.core.config.settings.PARTITION_SIZE

# Mapping of tables to their (current) surrogate id column and desired PK name
TABLES = {
    "end_users": {
        "pk_constraint": "end_users_pkey",
        "surrogate_id": "user_id",  # Updated to match current model
    },
    "products": {
        "pk_constraint": "products_pkey",
        "surrogate_id": "product_id",
    },
    "interactions": {
        "pk_constraint": "interactions_pkey",
        "surrogate_id": "id",  # Updated to match current model
    },
    "searches": {
        "pk_constraint": "searches_pkey",
        "surrogate_id": "id",  # Updated to match current model
    },
    "audit_logs": {
        "pk_constraint": "audit_logs_pkey",
        "surrogate_id": "id",  # Updated to match current model
    },
    "account_usage_metrics": {
        "pk_constraint": "account_usage_metrics_pkey",
        "surrogate_id": None,  # already only account_id
    },
}


def _ensure_composite_primary_key(table: str, pk_name: str, surrogate_id: str | None):
    """Drop existing PK (if any) and recreate it to include account_id.

    Uses CASCADE to remove dependent foreign-key constraints first to avoid
    `DependentObjectsStillExistError` when other tables still reference the
    previous single-column primary key. The affected FK constraints will be
    recreated later in the migration chain when their respective tables are
    processed.
    """
    # Build desired column list
    desired_cols = ["account_id"] + ([surrogate_id] if surrogate_id else [])

    # Check current primary key structure
    op.execute(
        f"""
        DO $$
        DECLARE
            current_pk_cols text[];
            desired_pk_cols text[] := ARRAY{desired_cols};
        BEGIN
            -- Get current primary key columns
            SELECT array_agg(a.attname ORDER BY k.ordinality)
            INTO current_pk_cols
            FROM pg_constraint c
            JOIN pg_class t ON c.conrelid = t.oid
            JOIN unnest(c.conkey) WITH ORDINALITY k(attnum, ordinality) ON true
            JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = k.attnum
            WHERE t.relname = '{table}' AND c.contype = 'p';

            -- Only recreate if different from desired structure
            IF current_pk_cols IS DISTINCT FROM desired_pk_cols THEN
                RAISE NOTICE 'Updating primary key for table {table} from % to %', current_pk_cols, desired_pk_cols;

                -- Drop existing PK (and any dependent FKs) if it exists
                EXECUTE format('ALTER TABLE %I DROP CONSTRAINT IF EXISTS %I CASCADE;', '{table}', '{pk_name}');

                -- Re-create PK with composite key
                EXECUTE format('ALTER TABLE %I ADD CONSTRAINT %I PRIMARY KEY (%s);',
                              '{table}', '{pk_name}', array_to_string(desired_pk_cols, ', '));
            ELSE
                RAISE NOTICE 'Primary key for table {table} already has correct structure: %', current_pk_cols;
            END IF;
        END;$$;
        """
    )


def _partition_table(table: str):
    """Convert a plain table into a partitioned table by RANGE(account_id).

    • Only runs if the Postgres server supports converting an existing table
      to partitioned (≥ v12, server_version_num >= 120000).
    • Uses dynamic EXECUTE to avoid PL/pgSQL parse-time errors on older
      versions. This keeps the migration idempotent regardless of the target
      instance version configured in each environment.
    """
    op.execute(
        f"""
        DO $$
        DECLARE
            v INT := current_setting('server_version_num')::INT;
        BEGIN
            -- Require PostgreSQL 12+
            IF v >= 120000 THEN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_partitioned_table WHERE partrelid = '{table}'::regclass
                ) THEN
                    BEGIN
                        EXECUTE format('ALTER TABLE %I PARTITION BY RANGE (account_id);', '{table}');
                    EXCEPTION WHEN others THEN
                        RAISE NOTICE 'Skipping partition conversion for table % due to error: %', '{table}', SQLERRM;
                    END;
                END IF;
            END IF;
        END;$$;
        """
    )


def _create_initial_partition(table: str):
    """Create an initial partition [0, PARTITION_SIZE) if it does not exist."""
    # First check if the table is actually partitioned before creating partitions
    op.execute(
        f"""
        DO $$
        BEGIN
            -- Only create partition if table is actually partitioned
            IF EXISTS (
                SELECT 1 FROM pg_partitioned_table WHERE partrelid = '{table}'::regclass
            ) THEN
                -- Check if partition doesn't already exist
                IF NOT EXISTS (
                    SELECT 1 FROM pg_tables WHERE tablename = '{table}_p_0_{PARTITION_SIZE}'
                ) THEN
                    EXECUTE format('CREATE TABLE %I PARTITION OF %I FOR VALUES FROM (0) TO (%s);',
                                   '{table}_p_0_{PARTITION_SIZE}', '{table}', {PARTITION_SIZE});
                    RAISE NOTICE 'Created partition {table}_p_0_{PARTITION_SIZE}';
                ELSE
                    RAISE NOTICE 'Partition {table}_p_0_{PARTITION_SIZE} already exists';
                END IF;
            ELSE
                RAISE NOTICE 'Table {table} is not partitioned, skipping partition creation';
            END IF;
        END;$$;
        """
    )


def upgrade() -> None:
    """Apply range partitioning to high-volume tables."""
    for tbl, cfg in TABLES.items():
        _ensure_composite_primary_key(tbl, cfg["pk_constraint"], cfg["surrogate_id"])
        _partition_table(tbl)
        _create_initial_partition(tbl)


def downgrade() -> None:
    """Reverting partitioning safely is not supported automatically."""
    raise RuntimeError("Downgrade not supported for partitioning migration.") 