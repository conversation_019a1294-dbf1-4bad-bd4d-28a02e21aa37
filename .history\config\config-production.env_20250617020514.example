# Configuración de Producción para Rayuela Backend

# Environment
ENV=production

# Base de datos PostgreSQL
DB_HOST=*************
DB_PORT=5432
DB_USER=rayuela_user
DB_NAME=rayuela_prod
# DB_PASSWORD se obtiene de Secret Manager

# Redis
REDIS_HOST=*************
REDIS_PORT=6379
# REDIS_PASSWORD se obtiene de Secret Manager

# Seguridad
# SECRET_KEY se obtiene de Secret Manager

# GCP Configuration
GCP_PROJECT_ID=your-project-id
GCP_REGION=us-central1

# Configuración de aplicación
DEBUG=False
ALLOWED_HOSTS=api.rayuela.com,backend.rayuela.com
ALLOWED_ORIGINS=https://rayuela.com,https://app.rayuela.com,https://rayuela-frontend-prod.run.app

# Logging
LOG_LEVEL=INFO

# Gunicorn Optimization Settings (optimized for startup costs)
# Reduced worker_connections to align with SQLAlchemy pool configuration
# Total connections: workers * worker_connections = 2 * 20 = 40 logical connections
# SQLAlchemy pool: pool_size=20 + max_overflow=5 = 25 physical connections
# This allows using smaller Cloud SQL instances (db-f1-micro, db-g1-small)
GUNICORN_WORKERS=2
GUNICORN_WORKER_CONNECTIONS=20
GUNICORN_TIMEOUT=120

# Workers Celery
CELERY_BROKER_URL=redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/0
CELERY_RESULT_BACKEND=redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/0

# Base de datos URL (se construye automáticamente)
DATABASE_URL=postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}

# Health check
HEALTH_CHECK_PATH=/health 