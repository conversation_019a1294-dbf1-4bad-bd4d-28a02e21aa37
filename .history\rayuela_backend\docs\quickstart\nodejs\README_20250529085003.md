# Rayuela API - Guía de Inicio Rápido para Node.js

Esta guía te mostrará cómo integrar Rayuela en tu aplicación Node.js, desde el registro hasta obtener recomendaciones personalizadas.

## Requisitos

- Node.js 14 o superior
- npm o yarn

## Instalación

```bash
npm install axios dotenv
# o con yarn
yarn add axios dotenv
```

## 1. Registro y Obtención de API Key

Primero, necesitas registrarte y obtener tu API Key:

```javascript
const axios = require('axios');

async function registerAccount() {
  const url = 'https://api.rayuela.com/v1/auth/register';
  const payload = {
    email: '<EMAIL>',
    password: 'Tu_Contraseña_Segura123',
    account_name: 'Mi Empresa'
  };
  const headers = {
    'Content-Type': 'application/json'
  };

  try {
    const response = await axios.post(url, payload, { headers });
    const data = response.data;

    // Guarda tu API Key de forma segura
    const apiKey = data.api_key;
    const accessToken = data.access_token;

    console.log('Registro exitoso!');
    console.log(`Tu API Key: ${apiKey}`);
    console.log('IMPORTANTE: Guarda esta clave en un lugar seguro. Solo se muestra una vez.');

    return {
      apiKey,
      accessToken
    };
  } catch (error) {
    console.error('Error en el registro:', error.response ? error.response.data : error.message);
    return null;
  }
}

// Ejecutar el registro
async function main() {
  const credentials = await registerAccount();
  if (credentials) {
    // Continuar con el resto del flujo
  }
}

main().catch(console.error);
```

## 2. Configuración del Cliente

Crea un cliente reutilizable para interactuar con la API:

```javascript
class RayuelaClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://api.rayuela.com/v1';
    this.headers = {
      'X-API-Key': apiKey,
      'Content-Type': 'application/json'
    };
  }

  async checkAuth() {
    const url = `${this.baseUrl}/health/auth`;
    const response = await axios.get(url, { headers: this.headers });
    return response.data;
  }

  async ingestBatchData(products = [], users = [], interactions = []) {
    const url = `${this.baseUrl}/ingestion/batch`;
    const payload = {};

    if (products.length > 0) {
      payload.products = products;
    }
    if (users.length > 0) {
      payload.users = users;
    }
    if (interactions.length > 0) {
      payload.interactions = interactions;
    }

    const response = await axios.post(url, payload, { headers: this.headers });
    return response.data;
  }

  async checkJobStatus(jobId) {
    const url = `${this.baseUrl}/ingestion/batch/${jobId}`;
    const response = await axios.get(url, { headers: this.headers });
    return response.data;
  }

  async trainModel(modelType = 'collaborative_filtering', hyperparameters = {}) {
    const url = `${this.baseUrl}/pipeline/train`;
    const payload = {
      model_type: modelType,
      hyperparameters
    };

    const response = await axios.post(url, payload, { headers: this.headers });
    return response.data;
  }

  async checkTrainingStatus(jobId) {
    const url = `${this.baseUrl}/pipeline/jobs/${jobId}/status`;
    const response = await axios.get(url, { headers: this.headers });
    return response.data;
  }

  async getRecommendations(userId, limit = 10, filters = null) {
    const url = `${this.baseUrl}/recommendations/personalized/query`;
    const payload = {
      user_id: userId,
      limit
    };

    if (filters) {
      payload.filters = filters;
    }

    const response = await axios.post(url, payload, { headers: this.headers });
    return response.data;
  }
}

// Crear cliente con la API Key obtenida
const client = new RayuelaClient(credentials.apiKey);
```

## 3. Verificación de Autenticación

```javascript
async function verifyAuthentication() {
  try {
    const result = await client.checkAuth();
    console.log('Autenticación exitosa:', result);
    return true;
  } catch (error) {
    console.error('Error de autenticación:', error.response ? error.response.data : error.message);
    return false;
  }
}

// Verificar autenticación
const isAuthenticated = await verifyAuthentication();
```

## 4. Ingesta de Datos

```javascript
async function ingestSampleData() {
  // Datos de ejemplo
  const products = [
    { product_id: 'p1', name: 'Producto 1', category: 'Electrónica', price: 99.99 },
    { product_id: 'p2', name: 'Producto 2', category: 'Ropa', price: 49.99 },
    { product_id: 'p3', name: 'Producto 3', category: 'Hogar', price: 29.99 }
  ];

  const users = [
    { user_id: 'u1', age: 28, gender: 'M' },
    { user_id: 'u2', age: 35, gender: 'F' }
  ];

  const interactions = [
    { user_id: 'u1', product_id: 'p1', interaction_type: 'view', timestamp: '2023-01-01T10:30:00Z' },
    { user_id: 'u1', product_id: 'p2', interaction_type: 'purchase', timestamp: '2023-01-01T11:45:00Z' },
    { user_id: 'u2', product_id: 'p2', interaction_type: 'view', timestamp: '2023-01-02T09:15:00Z' },
    { user_id: 'u2', product_id: 'p3', interaction_type: 'purchase', timestamp: '2023-01-02T14:20:00Z' }
  ];

  try {
    const result = await client.ingestBatchData(products, users, interactions);
    const jobId = result.job_id;
    console.log(`Ingesta iniciada. Job ID: ${jobId}`);
    return jobId;
  } catch (error) {
    console.error('Error en la ingesta:', error.response ? error.response.data : error.message);
    return null;
  }
}

// Ingestar datos de ejemplo
const ingestionJobId = await ingestSampleData();
```

## 5. Verificar Estado del Trabajo de Ingesta

```javascript
async function waitForJobCompletion(jobId, maxWaitSeconds = 300) {
  const startTime = Date.now();

  while ((Date.now() - startTime) < maxWaitSeconds * 1000) {
    try {
      const status = await client.checkJobStatus(jobId);
      console.log(`Estado del trabajo: ${status.status}`);

      if (['completed', 'failed'].includes(status.status)) {
        return status;
      }

      // Esperar 5 segundos antes de verificar nuevamente
      await new Promise(resolve => setTimeout(resolve, 5000));
    } catch (error) {
      console.error('Error al verificar estado:', error.response ? error.response.data : error.message);
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }

  console.log('Tiempo de espera agotado');
  return null;
}

// Esperar a que se complete la ingesta
let ingestionStatus = null;
if (ingestionJobId) {
  ingestionStatus = await waitForJobCompletion(ingestionJobId);
  console.log('Resultado final de ingesta:', ingestionStatus);
}
```

## 6. Entrenamiento del Modelo

```javascript
async function trainRecommendationModel() {
  try {
    const hyperparameters = {
      factors: 20,
      iterations: 20
    };

    const result = await client.trainModel(
      'collaborative_filtering',
      hyperparameters
    );

    const jobId = result.job_id;
    console.log(`Entrenamiento iniciado. Job ID: ${jobId}`);
    return jobId;
  } catch (error) {
    console.error('Error al iniciar entrenamiento:', error.response ? error.response.data : error.message);
    return null;
  }
}

// Entrenar modelo
const trainingJobId = await trainRecommendationModel();
```

## 7. Verificar Estado del Entrenamiento

```javascript
// Esperar a que se complete el entrenamiento
let trainingStatus = null;
if (trainingJobId) {
  trainingStatus = await waitForJobCompletion(trainingJobId, 600);
  console.log('Resultado final de entrenamiento:', trainingStatus);
}
```

## 8. Obtener Recomendaciones

```javascript
async function getPersonalizedRecommendations(userId = 'u1') {
  try {
    const filters = {
      categories: ['Electrónica', 'Hogar']
    };

    const recommendations = await client.getRecommendations(
      userId,
      5,
      filters
    );

    console.log(`Recomendaciones para usuario ${userId}:`);
    recommendations.items.forEach((item, index) => {
      console.log(`${index + 1}. ${item.name} (ID: ${item.product_id}, Score: ${item.score})`);
    });

    return recommendations;
  } catch (error) {
    console.error('Error al obtener recomendaciones:', error.response ? error.response.data : error.message);
    return null;
  }
}

// Obtener recomendaciones
const recommendations = await getPersonalizedRecommendations();
```

## Ejemplo Completo

Aquí tienes un script completo que puedes ejecutar:

```javascript
require('dotenv').config();
const axios = require('axios');

// Usar API Key existente o registrar una nueva cuenta
const apiKey = process.env.RAYUELA_API_KEY;

async function main() {
  let client;

  if (!apiKey) {
    // Código de registro (ver sección 1)
    const credentials = await registerAccount();
    if (!credentials) return;
    client = new RayuelaClient(credentials.apiKey);
  } else {
    client = new RayuelaClient(apiKey);
  }

  // Verificar autenticación
  const isAuthenticated = await verifyAuthentication();

  if (isAuthenticated) {
    // Ingestar datos
    const ingestionJobId = await ingestSampleData();

    // Esperar a que se complete la ingesta
    if (ingestionJobId) {
      const ingestionStatus = await waitForJobCompletion(ingestionJobId);

      if (ingestionStatus && ingestionStatus.status === 'completed') {
        // Entrenar modelo
        const trainingJobId = await trainRecommendationModel();

        // Esperar a que se complete el entrenamiento
        if (trainingJobId) {
          const trainingStatus = await waitForJobCompletion(trainingJobId, 600);

          if (trainingStatus && trainingStatus.status === 'completed') {
            // Obtener recomendaciones
            const recommendations = await getPersonalizedRecommendations();
          }
        }
      }
    }
  }
}

main().catch(console.error);
```

## Documentación de Referencia de la API

Para obtener información detallada sobre todos los endpoints disponibles, parámetros y esquemas de datos, consulta la documentación interactiva de la API:

- **Swagger UI**: [/api/docs](/api/docs) - Interfaz interactiva para explorar y probar la API
- **OpenAPI JSON**: [/api/openapi.json](/api/openapi.json) - Especificación OpenAPI en formato JSON
- **ReDoc**: [/api/redoc](/api/redoc) - Documentación alternativa con un diseño más limpio

La documentación interactiva te permite:

- Explorar todos los endpoints disponibles
- Ver los parámetros requeridos y opcionales para cada endpoint
- Probar los endpoints directamente desde el navegador con la función "Try it out"
- Ver los esquemas de solicitud y respuesta para cada endpoint

## Recursos Adicionales

- [Guía General de Inicio Rápido](../../QUICKSTART.md)
- [Guías para Otros Lenguajes](../)
- [Repositorio de Ejemplos](https://github.com/rayuela-examples)
- [Soporte](mailto:<EMAIL>)
