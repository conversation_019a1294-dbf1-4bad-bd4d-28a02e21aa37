# Rayuela API - Guía de Inicio Rápido para PHP

Esta guía te mostrará cómo integrar Rayuela en tu aplicación PHP, desde el registro hasta obtener recomendaciones personalizadas.

## Requisitos

- PHP 7.4 o superior
- Extensión cURL
- Composer (recomendado)

## Instalación con Composer

```bash
composer require guzzlehttp/guzzle
```

## 1. Registro y Obtención de API Key

Primero, necesitas registrarte y obtener tu API Key:

```php
<?php
require 'vendor/autoload.php';

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

function registerAccount() {
    $client = new Client();
    $url = 'https://api.rayuela.com/v1/auth/register';
    $payload = [
        'json' => [
            'email' => '<EMAIL>',
            'password' => 'Tu_Contraseña_Segura123',
            'account_name' => 'Mi Empresa'
        ],
        'headers' => [
            'Content-Type' => 'application/json'
        ]
    ];

    try {
        $response = $client->post($url, $payload);
        $data = json_decode($response->getBody(), true);

        // Guarda tu API Key de forma segura
        $apiKey = $data['api_key'];
        $accessToken = $data['access_token'];

        echo "Registro exitoso!\n";
        echo "Tu API Key: {$apiKey}\n";
        echo "IMPORTANTE: Guarda esta clave en un lugar seguro. Solo se muestra una vez.\n";

        return [
            'apiKey' => $apiKey,
            'accessToken' => $accessToken
        ];
    } catch (RequestException $e) {
        echo "Error en el registro: ";
        if ($e->hasResponse()) {
            echo $e->getResponse()->getBody();
        } else {
            echo $e->getMessage();
        }
        echo "\n";
        return null;
    }
}

// Ejecutar el registro
$credentials = registerAccount();
```

## 2. Cliente API Completo

A continuación, una implementación más completa del cliente de Rayuela API:

```php
<?php
require 'vendor/autoload.php';

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class RayuelaClient {
    private $apiKey;
    private $baseUrl;
    private $client;

    public function __construct($apiKey, $baseUrl = 'https://api.rayuela.com/v1') {
        $this->apiKey = $apiKey;
        $this->baseUrl = $baseUrl;
        $this->client = new Client([
            'base_uri' => $this->baseUrl,
            'headers' => [
                'X-API-Key' => $this->apiKey,
                'Content-Type' => 'application/json'
            ]
        ]);
    }

    /**
     * Verifica que la autenticación con la API Key funcione correctamente
     */
    public function checkAuth() {
        try {
            $response = $this->client->get('/health/auth');
            return json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            $this->handleRequestException($e);
        }
    }

    /**
     * Obtiene recomendaciones personalizadas para un usuario
     */
    public function getRecommendations($userId, $limit = 10, $filters = null, $context = null) {
        $payload = [
            'json' => [
                'user_id' => $userId,
                'limit' => $limit
            ]
        ];

        if ($filters) {
            $payload['json']['filters'] = $filters;
        }

        if ($context) {
            $payload['json']['context'] = $context;
        }

        try {
            $response = $this->client->post('/recommendations/personalized/query', $payload);
            return json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            $this->handleRequestException($e);
        }
    }

    /**
     * Ingesta de datos por lotes (productos, usuarios y/o interacciones)
     */
    public function batchIngestion($data) {
        try {
            $response = $this->client->post('/ingestion/batch', [
                'json' => $data
            ]);
            return json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            $this->handleRequestException($e);
        }
    }

    /**
     * Verifica el estado de un trabajo de ingesta por lotes
     */
    public function getBatchIngestionStatus($jobId) {
        try {
            $response = $this->client->get("/ingestion/batch/{$jobId}");
            return json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            $this->handleRequestException($e);
        }
    }

    /**
     * Inicia un trabajo de entrenamiento de modelo
     */
    public function trainModel($modelType = 'standard', $hyperparameters = null, $force = false) {
        $payload = [
            'json' => [
                'model_type' => $modelType,
                'force' => $force
            ]
        ];

        if ($hyperparameters) {
            $payload['json']['hyperparameters'] = $hyperparameters;
        }

        try {
            $response = $this->client->post('/pipeline/train', $payload);
            return json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            $this->handleRequestException($e);
        }
    }

    /**
     * Verifica el estado de un trabajo de entrenamiento
     */
    public function getTrainingStatus($jobId) {
        try {
            $response = $this->client->get("/pipeline/jobs/{$jobId}/status");
            return json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            $this->handleRequestException($e);
        }
    }

    /**
     * Lista los modelos entrenados
     */
    public function getModels($skip = 0, $limit = 10) {
        try {
            $response = $this->client->get("/pipeline/models?skip={$skip}&limit={$limit}");
            return json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            $this->handleRequestException($e);
        }
    }

    /**
     * Manejo centralizado de excepciones
     */
    private function handleRequestException($e) {
        if ($e->hasResponse()) {
            $response = $e->getResponse();
            $statusCode = $response->getStatusCode();
            $body = json_decode($response->getBody(), true);
            
            // Los errores de la API de Rayuela siguen un formato estándar
            $errorCode = $body['error_code'] ?? 'UNKNOWN_ERROR';
            $message = $body['message'] ?? 'Error desconocido';
            
            throw new Exception("[{$statusCode}] [{$errorCode}] {$message}", $statusCode);
        } else {
            throw new Exception("Error de red: " . $e->getMessage());
        }
    }
}
```

## 3. Uso Completo: Flujo desde la Ingesta hasta las Recomendaciones

El siguiente ejemplo demuestra un flujo completo de integración con Rayuela:

```php
<?php
require 'vendor/autoload.php';
require 'RayuelaClient.php'; // Clase definida en el paso anterior

// 1. Inicializar cliente
$apiKey = 'sk_aBcDeFgHiJkLmNoPqRsTuVwXyZ0123456789'; // Tu API Key
$client = new RayuelaClient($apiKey);

// 2. Verificar autenticación
try {
    $authResult = $client->checkAuth();
    echo "✅ Autenticación exitosa\n";
} catch (Exception $e) {
    die("❌ Error de autenticación: " . $e->getMessage() . "\n");
}

// 3. Ingestar datos de ejemplo
$batchData = [
    'products' => [
        [
            'external_id' => 'P001',
            'name' => 'Smartphone XYZ',
            'category' => 'Electrónica',
            'price' => 599.99,
            'description' => 'Smartphone de última generación',
            'image_url' => 'https://ejemplo.com/images/p001.jpg',
            'metadata' => [
                'brand' => 'XYZ',
                'color' => 'Negro',
                'storage' => '128GB'
            ]
        ],
        [
            'external_id' => 'P002',
            'name' => 'Tablet ABC',
            'category' => 'Electrónica',
            'price' => 399.99,
            'description' => 'Tablet de alto rendimiento',
            'image_url' => 'https://ejemplo.com/images/p002.jpg',
            'metadata' => [
                'brand' => 'ABC',
                'color' => 'Plata',
                'storage' => '64GB'
            ]
        ]
    ],
    'users' => [
        [
            'external_id' => 'U001',
            'email' => '<EMAIL>',
            'metadata' => [
                'age' => 28,
                'gender' => 'M',
                'location' => 'Barcelona'
            ]
        ]
    ],
    'interactions' => [
        [
            'user_external_id' => 'U001',
            'product_external_id' => 'P001',
            'interaction_type' => 'view',
            'timestamp' => date('Y-m-d\TH:i:s\Z', time() - 3600) // Hace 1 hora
        ],
        [
            'user_external_id' => 'U001',
            'product_external_id' => 'P002',
            'interaction_type' => 'view',
            'timestamp' => date('Y-m-d\TH:i:s\Z', time() - 1800) // Hace 30 minutos
        ],
        [
            'user_external_id' => 'U001',
            'product_external_id' => 'P001',
            'interaction_type' => 'add_to_cart',
            'timestamp' => date('Y-m-d\TH:i:s\Z', time() - 900) // Hace 15 minutos
        ]
    ]
];

try {
    $ingestionResult = $client->batchIngestion($batchData);
    $jobId = $ingestionResult['job_id'];
    echo "✅ Ingesta iniciada. Job ID: {$jobId}\n";
    
    // Esperar a que la ingesta complete (para ejemplo demostrativo)
    echo "⏳ Esperando a que la ingesta complete...\n";
    $completed = false;
    $maxAttempts = 10;
    $attempts = 0;
    
    while (!$completed && $attempts < $maxAttempts) {
        sleep(2); // Esperar 2 segundos entre verificaciones
        $jobStatus = $client->getBatchIngestionStatus($jobId);
        $status = $jobStatus['status'];
        echo "   Estado: {$status} - Progreso: {$jobStatus['progress']}%\n";
        
        if (in_array($status, ['completed', 'failed', 'cancelled'])) {
            $completed = true;
        }
        $attempts++;
    }
    
    if ($status === 'completed') {
        echo "✅ Ingesta completada con éxito\n";
    } else {
        echo "⚠️ La ingesta no completó en el tiempo esperado o falló. Estado final: {$status}\n";
    }
} catch (Exception $e) {
    echo "❌ Error en la ingesta: " . $e->getMessage() . "\n";
}

// 4. Iniciar entrenamiento del modelo
try {
    $trainingResult = $client->trainModel('standard', null, true);
    $trainingJobId = $trainingResult['job_id'];
    echo "✅ Entrenamiento iniciado. Job ID: {$trainingJobId}\n";
    
    // Nota: En un entorno real, no esperarías aquí ya que el entrenamiento 
    // puede tomar varios minutos. En su lugar, implementarías un webhook
    // o un proceso periódico para verificar el estado.
    echo "⏳ En un entorno real, el entrenamiento tomaría varios minutos u horas.\n";
    echo "   Para este ejemplo, asumiremos que ya está completo.\n";
} catch (Exception $e) {
    echo "❌ Error al iniciar entrenamiento: " . $e->getMessage() . "\n";
}

// 5. Obtener recomendaciones personalizadas
try {
    $filters = [
        'logic' => 'and',
        'filters' => [
            [
                'field' => 'category',
                'op' => 'eq',
                'value' => 'Electrónica'
            ],
            [
                'field' => 'price',
                'op' => 'lt',
                'value' => 1000
            ]
        ]
    ];
    
    $context = [
        'page_type' => 'home',
        'device' => 'desktop'
    ];
    
    $recommendations = $client->getRecommendations('U001', 5, $filters, $context);
    
    echo "✅ Recomendaciones para usuario U001:\n";
    foreach ($recommendations['items'] as $index => $item) {
        $i = $index + 1;
        $name = $item['name'];
        $id = $item['id'];
        $score = number_format($item['score'], 2);
        echo "{$i}. {$name} (ID: {$id}, Score: {$score})\n";
    }
} catch (Exception $e) {
    echo "❌ Error al obtener recomendaciones: " . $e->getMessage() . "\n";
}
```

## 4. Manejo de Errores

Es importante implementar un manejo adecuado de los errores de la API. Rayuela utiliza códigos de error estandarizados que puedes usar para responder apropiadamente.

```php
<?php
// Ejemplo de manejo de errores específicos

try {
    $recommendations = $client->getRecommendations('usuario_inexistente', 5);
} catch (Exception $e) {
    $message = $e->getMessage();
    
    // Buscar el código de error
    if (strpos($message, 'USER_NOT_FOUND') !== false) {
        // El usuario no existe, quizás debamos crear uno nuevo
        echo "Usuario no encontrado. Creando perfil...\n";
        // Lógica para crear usuario
    } else if (strpos($message, 'MODEL_NOT_TRAINED') !== false) {
        // No hay modelo entrenado para esta cuenta
        echo "No hay modelo entrenado. Mostrando artículos populares en su lugar...\n";
        // Lógica para mostrar artículos populares
    } else if (strpos($message, 'RATE_LIMIT_EXCEEDED') !== false) {
        // Se excedió el límite de tasa
        echo "Demasiadas solicitudes. Intentando de nuevo en 5 segundos...\n";
        sleep(5);
        // Reintentar la solicitud
    } else {
        // Otros errores
        echo "Error: {$message}\n";
    }
}
```

## 5. Ejemplo: Flujo de E-commerce

A continuación, un ejemplo más completo que simula una integración en un sitio de e-commerce:

```php
<?php
require 'vendor/autoload.php';
require 'RayuelaClient.php';

class EcommerceRecommendationService {
    private $rayuelaClient;
    
    public function __construct($apiKey) {
        $this->rayuelaClient = new RayuelaClient($apiKey);
    }
    
    /**
     * Registra una vista de producto
     */
    public function trackProductView($userId, $productId) {
        $interaction = [
            'interactions' => [
                [
                    'user_external_id' => $userId,
                    'product_external_id' => $productId,
                    'interaction_type' => 'view',
                    'timestamp' => date('Y-m-d\TH:i:s\Z')
                ]
            ]
        ];
        
        return $this->rayuelaClient->batchIngestion($interaction);
    }
    
    /**
     * Registra una compra de producto
     */
    public function trackPurchase($userId, $productId, $price, $quantity = 1) {
        $interaction = [
            'interactions' => [
                [
                    'user_external_id' => $userId,
                    'product_external_id' => $productId,
                    'interaction_type' => 'purchase',
                    'timestamp' => date('Y-m-d\TH:i:s\Z'),
                    'metadata' => [
                        'price' => $price,
                        'quantity' => $quantity
                    ]
                ]
            ]
        ];
        
        return $this->rayuelaClient->batchIngestion($interaction);
    }
    
    /**
     * Obtiene recomendaciones para mostrar en la página de inicio
     */
    public function getHomePageRecommendations($userId, $device = 'desktop') {
        try {
            $context = [
                'page_type' => 'home',
                'device' => $device
            ];
            
            return $this->rayuelaClient->getRecommendations($userId, 10, null, $context);
        } catch (Exception $e) {
            // Si hay algún error con las recomendaciones personalizadas,
            // podemos caer en recomendaciones de productos populares
            error_log("Error al obtener recomendaciones personalizadas: " . $e->getMessage());
            return $this->getFallbackRecommendations();
        }
    }
    
    /**
     * Obtiene recomendaciones para mostrar en la página de producto
     */
    public function getProductPageRecommendations($userId, $currentProductId, $category) {
        try {
            $context = [
                'page_type' => 'product_detail',
                'device' => 'desktop',
                'source_item_id' => $currentProductId
            ];
            
            $filters = [
                'logic' => 'and',
                'filters' => [
                    [
                        'field' => 'category',
                        'op' => 'eq',
                        'value' => $category
                    ],
                    [
                        'field' => 'id',
                        'op' => 'neq',
                        'value' => $currentProductId
                    ]
                ]
            ];
            
            return $this->rayuelaClient->getRecommendations($userId, 4, $filters, $context);
        } catch (Exception $e) {
            error_log("Error al obtener recomendaciones para página de producto: " . $e->getMessage());
            return $this->getSimilarProducts($currentProductId);
        }
    }
    
    /**
     * Recomendaciones de reserva en caso de error
     */
    private function getFallbackRecommendations() {
        // Lógica para obtener productos populares u otros fallbacks
        return [
            'items' => [
                /* Array de productos populares */
            ]
        ];
    }
    
    /**
     * Obtiene productos similares usando otro endpoint
     */
    private function getSimilarProducts($productId) {
        try {
            $response = $this->rayuelaClient->client->get("/products/{$productId}/similar?limit=4");
            return json_decode($response->getBody(), true);
        } catch (Exception $e) {
            error_log("Error al obtener productos similares: " . $e->getMessage());
            return ['items' => []];
        }
    }
}

// Ejemplo de uso en una página de producto
$userId = $_SESSION['user_id'] ?? 'anonymous_' . session_id();
$productId = $_GET['product_id'];
$category = $_GET['category'];

$recommendationService = new EcommerceRecommendationService('sk_tu_api_key');

// Registrar la vista del producto
$recommendationService->trackProductView($userId, $productId);

// Obtener y mostrar recomendaciones
$recommendations = $recommendationService->getProductPageRecommendations($userId, $productId, $category);

// En tu template/vista:
foreach ($recommendations['items'] as $product) {
    // Renderizar tarjeta de producto
    echo '<div class="product-card">';
    echo '<img src="' . $product['image_url'] . '" alt="' . $product['name'] . '">';
    echo '<h3>' . $product['name'] . '</h3>';
    echo '<p class="price">$' . number_format($product['price'], 2) . '</p>';
    echo '<button class="add-to-cart">Añadir al carrito</button>';
    echo '</div>';
}
```

## 6. Referencias de cURL

Algunos ejemplos de cURL equivalentes para referencia rápida:

### Autenticación

```bash
curl -X GET "https://api.rayuela.com/v1/health/auth" \
  -H "X-API-Key: sk_tu_api_key"
```

### Ingesta por Lotes

```bash
curl -X POST "https://api.rayuela.com/v1/ingestion/batch" \
  -H "X-API-Key: sk_tu_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "products": [
      {
        "external_id": "P001",
        "name": "Smartphone XYZ",
        "category": "Electrónica",
        "price": 599.99
      }
    ],
    "users": [
      {
        "external_id": "U001",
        "email": "<EMAIL>"
      }
    ],
    "interactions": [
      {
        "user_external_id": "U001",
        "product_external_id": "P001",
        "interaction_type": "view",
        "timestamp": "2023-06-01T10:30:00Z"
      }
    ]
  }'
```

### Entrenamiento de Modelo

```bash
curl -X POST "https://api.rayuela.com/v1/pipeline/train" \
  -H "X-API-Key: sk_tu_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "model_type": "standard",
    "force": true
  }'
```

### Recomendaciones Personalizadas

```bash
curl -X POST "https://api.rayuela.com/v1/recommendations/personalized/query" \
  -H "X-API-Key: sk_tu_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "U001",
    "filters": {
      "logic": "and",
      "filters": [
        {
          "field": "category",
          "op": "eq",
          "value": "Electrónica"
        }
      ]
    },
    "limit": 5
  }'
```

## Documentación de Referencia de la API

Para obtener información detallada sobre todos los endpoints disponibles, parámetros y esquemas de datos, consulta la documentación interactiva de la API:

- **Swagger UI**: [/api/docs](/api/docs) - Interfaz interactiva para explorar y probar la API
- **OpenAPI JSON**: [/api/openapi.json](/api/openapi.json) - Especificación OpenAPI en formato JSON
- **ReDoc**: [/api/redoc](/api/redoc) - Documentación alternativa con un diseño más limpio

La documentación interactiva te permite:

- Explorar todos los endpoints disponibles
- Ver los parámetros requeridos y opcionales para cada endpoint
- Probar los endpoints directamente desde el navegador con la función "Try it out"
- Ver los esquemas de solicitud y respuesta para cada endpoint

## Recursos Adicionales

- [Guía General de Inicio Rápido](../../QUICKSTART.md)
- [Guías para Otros Lenguajes](../)
- [Repositorio de Ejemplos](https://github.com/rayuela-examples)
- [Soporte](mailto:<EMAIL>)
