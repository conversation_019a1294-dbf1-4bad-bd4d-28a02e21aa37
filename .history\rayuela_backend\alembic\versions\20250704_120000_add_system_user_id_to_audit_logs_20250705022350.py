"""Consolidate all table structure fixes and add system_user_id to audit_logs

Revision ID: 20250704_120000
Revises: 20250701_020000
Create Date: 2025-07-04 12:00:00
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = "20250704_120000"
down_revision: Union[str, None] = "20250701_020000"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def _column_exists(table_name: str, column_name: str) -> bool:
    connection = op.get_bind()
    result = connection.execute(
        text(
            """
        SELECT column_name FROM information_schema.columns
        WHERE table_name = :table_name
          AND column_name = :column_name
          AND table_schema = 'public'
        """
        ),
        {"table_name": table_name, "column_name": column_name},
    )
    return result.fetchone() is not None


def _constraint_exists(constraint_name: str, table_name: str) -> bool:
    """Helper function to check if a constraint exists"""
    connection = op.get_bind()
    result = connection.execute(
        text(
            """
        SELECT constraint_name FROM information_schema.table_constraints
        WHERE table_name = :table_name
          AND constraint_name = :constraint_name
          AND table_schema = 'public'
        """
        ),
        {"table_name": table_name, "constraint_name": constraint_name},
    )
    return result.fetchone() is not None


def _table_exists(table_name: str) -> bool:
    """Check if a table exists."""
    connection = op.get_bind()
    result = connection.execute(
        text("""
        SELECT 1 FROM information_schema.tables
        WHERE table_name = :table_name
        """),
        {"table_name": table_name}
    )
    return result.fetchone() is not None


def upgrade() -> None:
    """Add system_user_id column to audit_logs (simplified approach)."""

    print("Adding system_user_id column to audit_logs...")

    # Step 1: Add system_user_id column to audit_logs
    if not _column_exists("audit_logs", "system_user_id"):
        print("  Adding system_user_id column...")
        op.add_column("audit_logs", sa.Column("system_user_id", sa.Integer(), nullable=True))
        print("  Column added successfully!")
    else:
        print("  system_user_id column already exists, skipping...")

    print("Migration completed successfully!")


def downgrade() -> None:
    """Remove system_user_id column from audit_logs."""

    print("Removing system_user_id column from audit_logs...")

    # Drop system_user_id column from audit_logs
    if _column_exists("audit_logs", "system_user_id"):
        print("  Dropping system_user_id column...")
        op.drop_column("audit_logs", "system_user_id")
        print("  Column dropped successfully!")
    else:
        print("  system_user_id column does not exist, skipping...")

    print("Downgrade completed successfully!")