from pydantic import BaseModel, <PERSON>
from datetime import datetime
from typing import Optional, Dict, Any, Union
from src.db.enums import InteractionType


class InteractionBase(BaseModel):
    end_user_id: int  # Match database schema
    product_id: int
    interaction_type: InteractionType
    rating: float  # Match database schema (was 'value')
    session_id: Optional[str] = None  # Match database schema
    context: Optional[Dict[str, Any]] = None  # Match database schema (was 'recommendation_metadata')


class InteractionExternalCreate(BaseModel):
    """Schema for creating interactions using external IDs."""
    external_user_id: str = Field(..., description="External user identifier provided by the client")
    external_product_id: str = Field(..., description="External product identifier provided by the client")
    interaction_type: InteractionType
    rating: float  # Match database schema (was 'value')
    session_id: Optional[str] = None  # Match database schema
    context: Optional[Dict[str, Any]] = None  # Match database schema (was 'recommendation_metadata')


class InteractionCreate(InteractionBase):
    pass


class Interaction(InteractionBase):
    interaction_id: int  # Match database schema
    account_id: int
    timestamp: datetime
    created_at: datetime  # Match database schema
    updated_at: datetime  # Match database schema

    class ConfigDict:
        from_attributes = True


class InteractionExternal(BaseModel):
    """Schema for interaction responses that include external IDs."""
    id: int
    account_id: int
    external_user_id: str = Field(..., description="External user identifier")
    external_product_id: str = Field(..., description="External product identifier")
    interaction_type: InteractionType
    value: float
    timestamp: datetime
    recommendation_metadata: Optional[Dict[str, Any]] = None

    class ConfigDict:
        from_attributes = True
