"""consolidated_initial_migration

Revision ID: ea4920022505
Revises:
Create Date: 2025-07-05 02:57:19.735921

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'ea4920022505'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create all tables with correct structure."""
    print("🚀 Starting consolidated initial migration...")

    # Create extensions
    print("📦 Creating PostgreSQL extensions...")
    op.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm;")
    op.execute("CREATE EXTENSION IF NOT EXISTS btree_gin;")

    # Create enums
    print("🏷️ Creating enums...")
    subscription_plan_enum = postgresql.ENUM(
        'FREE', 'BASIC', 'PREMIUM', 'ENTERPRISE',
        name='subscriptionplan',
        create_type=False
    )
    subscription_plan_enum.create(op.get_bind(), checkfirst=True)

    role_type_enum = postgresql.ENUM(
        'ADMIN', 'USER', 'VIEWER',
        name='roletype',
        create_type=False
    )
    role_type_enum.create(op.get_bind(), checkfirst=True)

    # Create accounts table (base table)
    print("🏢 Creating accounts table...")
    op.create_table('accounts',
        sa.Column('account_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('mercadopago_customer_id', sa.String(length=255), nullable=True, comment='Mercado Pago Customer ID'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('onboarding_checklist_status', sa.JSON(), nullable=True, comment='JSON object storing the status of onboarding checklist items'),
        sa.PrimaryKeyConstraint('account_id')
    )
    op.create_index(op.f('ix_accounts_mercadopago_customer_id'), 'accounts', ['mercadopago_customer_id'], unique=False)

    # Create subscriptions table (1:1 with accounts)
    print("💳 Creating subscriptions table...")
    op.create_table('subscriptions',
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('plan_type', postgresql.ENUM('FREE', 'BASIC', 'PREMIUM', 'ENTERPRISE', name='subscriptionplan'), nullable=False),
        sa.Column('api_calls_limit', sa.Integer(), nullable=True),
        sa.Column('storage_limit', sa.BigInteger(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('api_calls_used_current_period', sa.Integer(), nullable=True),
        sa.Column('storage_used_current_period', sa.BigInteger(), nullable=True),
        sa.Column('period_start', sa.DateTime(timezone=True), nullable=True),
        sa.Column('period_end', sa.DateTime(timezone=True), nullable=True),
        sa.Column('mercadopago_subscription_id', sa.String(length=255), nullable=True),
        sa.Column('mercadopago_price_id', sa.String(length=255), nullable=True),
        sa.Column('payment_gateway', sa.String(length=20), nullable=True),
        sa.Column('monthly_api_calls_used', sa.Integer(), nullable=False),
        sa.Column('storage_used', sa.BigInteger(), nullable=False),
        sa.Column('last_reset_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('available_models', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('additional_features', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('last_successful_training_at', sa.DateTime(timezone=True), nullable=True, comment='Fecha del último entrenamiento exitoso'),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('account_id')
    )
    op.create_index(op.f('ix_subscriptions_mercadopago_subscription_id'), 'subscriptions', ['mercadopago_subscription_id'], unique=False)
    op.create_index(op.f('ix_subscriptions_plan_type'), 'subscriptions', ['plan_type'], unique=False)
