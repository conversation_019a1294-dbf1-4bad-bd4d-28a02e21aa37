"""Add system_user_id column to audit_logs and FK to system_users

Revision ID: 20250704_120000
Revises: 20250701_020000
Create Date: 2025-07-04 12:00:00
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = "20250704_120000"
down_revision: Union[str, None] = "20250701_020000"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def _column_exists(table_name: str, column_name: str) -> bool:
    connection = op.get_bind()
    result = connection.execute(
        text(
            """
        SELECT column_name FROM information_schema.columns
        WHERE table_name = :table_name
          AND column_name = :column_name
          AND table_schema = 'public'
        """
        ),
        {"table_name": table_name, "column_name": column_name},
    )
    return result.fetchone() is not None


def upgrade() -> None:
    """Add system_user_id column and foreign key."""

    # First, ensure system_users table has 'id' column instead of 'user_id'
    if _column_exists("system_users", "user_id") and not _column_exists("system_users", "id"):
        print("Renaming system_users.user_id to system_users.id...")
        op.alter_column("system_users", "user_id", new_column_name="id")

    if not _column_exists("audit_logs", "system_user_id"):
        op.add_column("audit_logs", sa.Column("system_user_id", sa.Integer(), nullable=True))

    # Create composite foreign key (account_id, system_user_id) to system_users (account_id, id)
    # Check if FK already exists
    conn = op.get_bind()
    fk_exists = conn.execute(
        text(
            """
        SELECT constraint_name FROM information_schema.table_constraints
        WHERE table_name = 'audit_logs'
          AND constraint_type = 'FOREIGN KEY'
          AND constraint_name = 'fk_audit_log_system_user'
        """
        )
    ).fetchone()

    if not fk_exists:
        op.create_foreign_key(
            "fk_audit_log_system_user",
            "audit_logs",
            "system_users",
            ["account_id", "system_user_id"],
            ["account_id", "id"],
            ondelete="SET NULL",
        )


def downgrade() -> None:
    """Revert system_user_id addition"""
    op.drop_constraint("fk_audit_log_system_user", "audit_logs", type_="foreignkey")
    op.drop_column("audit_logs", "system_user_id") 