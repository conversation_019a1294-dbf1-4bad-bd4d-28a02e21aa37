steps:
  # 1. Build Backend Docker Image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-backend'
    args:
      - 'build'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '-f'
      - 'rayuela_backend/Dockerfile'
      - 'rayuela_backend'

  # 2. Push Backend image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-backend'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
    waitFor: ['build-backend']

  # 3. Deploy Backend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-backend'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🚀 Deploying backend to Cloud Run..."
        gcloud run deploy rayuela-backend \
          --image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID \
          --region=us-central1 \
          --platform=managed \
          --allow-unauthenticated \
          --service-account=rayuela-backend-sa@$PROJECT_ID.iam.gserviceaccount.com \
          --memory=2Gi \
          --cpu=1 \
          --timeout=300 \
          --concurrency=80 \
          --min-instances=0 \
          --max-instances=10 \
          --set-env-vars="ENVIRONMENT=production,GCP_PROJECT_ID=$PROJECT_ID,REGION=us-central1" \
          --set-secrets=POSTGRES_USER=POSTGRES_USER:latest,POSTGRES_DB=POSTGRES_DB:latest,POSTGRES_SERVER=POSTGRES_SERVER:latest,POSTGRES_PORT=POSTGRES_PORT:latest,POSTGRES_PASSWORD=POSTGRES_PASSWORD:latest \
          --set-secrets=REDIS_HOST=REDIS_HOST:latest,REDIS_PORT=REDIS_PORT:latest,REDIS_DB=REDIS_DB:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest,REDIS_URL=REDIS_URL:latest,GCS_BUCKET_NAME=GCS_BUCKET_NAME:latest,ALLOWED_ORIGINS=ALLOWED_ORIGINS:latest,ALLOWED_HOSTS=ALLOWED_HOSTS:latest \
          --vpc-connector=rayuela-vpc-connector \
          --project=$PROJECT_ID
        echo "✅ Backend deployment complete"
    waitFor: ['push-backend']

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'

timeout: '1200s'
