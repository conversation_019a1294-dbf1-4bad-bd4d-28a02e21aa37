#!/bin/bash

# 🧪 Script de prueba local del backend antes del deployment
# Ejecuta verificaciones exhaustivas para identificar problemas

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "🧪 PRUEBA LOCAL DEL BACKEND RAYUELA"
print_status "=================================="

# Verificar que estamos en el directorio correcto
if [ ! -f "rayuela_backend/main.py" ]; then
    print_error "Ejecuta este script desde el directorio raíz del proyecto (donde está rayuela_backend/)"
    exit 1
fi

cd rayuela_backend

print_status "📁 Directorio de trabajo: $(pwd)"

# 1. Verificar archivo de requirements
print_status "📋 Verificando requirements.txt..."
if [ ! -f "requirements.txt" ]; then
    print_error "requirements.txt no encontrado"
    exit 1
fi
print_success "requirements.txt encontrado"

# 2. Verificar archivos críticos
print_status "🔍 Verificando archivos críticos..."
critical_files=(
    "main.py"
    "gunicorn_conf.py" 
    "start.sh"
    "test_deps.py"
    "deployment_check.py"
    "Dockerfile"
)

missing_files=()
for file in "${critical_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "  ✅ $file"
    else
        print_error "  ❌ $file"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    print_error "Archivos críticos faltantes: ${missing_files[*]}"
    exit 1
fi

# 3. Probar construcción del Docker localmente
print_status "🐳 Probando construcción de Docker..."
if command -v docker &> /dev/null; then
    print_status "Construyendo imagen Docker localmente..."
    
    # Build the Docker image
    if docker build -t rayuela-backend-test:latest .; then
        print_success "✅ Imagen Docker construida exitosamente"
        
        # Test container startup (without running it)
        print_status "🔧 Probando startup del contenedor..."
        
        # Test with minimal environment variables
        docker run --rm \
            -e ENV=development \
            -e PORT=8080 \
            -e POSTGRES_USER=postgres \
            -e POSTGRES_PASSWORD=test \
            -e POSTGRES_SERVER=localhost \
            -e POSTGRES_PORT=5432 \
            -e POSTGRES_DB=test \
            -e REDIS_HOST=localhost \
            -e REDIS_PORT=6379 \
            -e SECRET_KEY=test-secret-key-for-local-testing-only \
            -e SKIP_SECRETS=true \
            -e SKIP_MIGRATIONS=true \
            rayuela-backend-test:latest \
            python deployment_check.py
        
        if [ $? -eq 0 ]; then
            print_success "✅ Contenedor puede ejecutar verificaciones básicas"
        else
            print_error "❌ Contenedor falló verificaciones básicas"
        fi
        
        # Clean up test image
        docker rmi rayuela-backend-test:latest
        
    else
        print_error "❌ Falló la construcción de Docker"
        exit 1
    fi
else
    print_warning "Docker no disponible - saltando prueba de contenedor"
fi

# 4. Verificar sintaxis de Python sin importar módulos complejos
print_status "🐍 Verificando sintaxis de Python..."

# Check main.py syntax
if python -m py_compile main.py; then
    print_success "  ✅ main.py sintaxis OK"
else
    print_error "  ❌ main.py errores de sintaxis"
    exit 1
fi

# Check deployment_check.py syntax
if python -m py_compile deployment_check.py; then
    print_success "  ✅ deployment_check.py sintaxis OK"
else
    print_error "  ❌ deployment_check.py errores de sintaxis"
    exit 1
fi

# 5. Verificar configuración de Cloud Build
print_status "☁️ Verificando configuración de Cloud Build..."
cd ..

if [ -f "cloudbuild-deploy-production.yaml" ]; then
    # Check for common issues in Cloud Build config
    if grep -q "WORKER_TYPE=api" cloudbuild-deploy-production.yaml; then
        print_success "  ✅ WORKER_TYPE=api configurado para backend principal"
    else
        print_warning "  ⚠️ WORKER_TYPE=api no encontrado en cloudbuild config"
    fi
    
    if grep -q "DB_PASSWORD:latest" cloudbuild-deploy-production.yaml; then
        print_success "  ✅ Configuración de secretos consistente"
    else
        print_warning "  ⚠️ Configuración de secretos puede tener inconsistencias"
    fi
    
    print_success "  ✅ cloudbuild-deploy-production.yaml encontrado"
else
    print_error "  ❌ cloudbuild-deploy-production.yaml no encontrado"
    exit 1
fi

print_status ""
print_status "🎉 RESUMEN DE VERIFICACIONES"
print_status "============================="
print_success "✅ Archivos críticos presentes"
print_success "✅ Sintaxis de Python válida"
print_success "✅ Configuración de Docker corregida" 
print_success "✅ Configuración de Cloud Build verificada"

print_status ""
print_status "🚀 SIGUIENTES PASOS:"
print_status "1. Ejecutar: scripts/deploy-production.sh --direct"
print_status "2. Verificar logs de Cloud Build"
print_status "3. Probar endpoint: https://BACKEND_URL/health"

print_status ""
print_success "🎯 El backend está listo para deployment!" 