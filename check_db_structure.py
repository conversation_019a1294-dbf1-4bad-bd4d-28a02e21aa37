#!/usr/bin/env python3
import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'rayuela_backend'))

from sqlalchemy import text
from src.db.database import get_db_engine

async def check_tables():
    engine = get_db_engine()
    async with engine.begin() as conn:
        # Check roles table structure
        result = await conn.execute(text("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'roles' ORDER BY ordinal_position"))
        print('Roles table columns:')
        for row in result:
            print(f'  {row[0]}: {row[1]}')
        
        # Check primary key
        result = await conn.execute(text("SELECT constraint_name, column_name FROM information_schema.key_column_usage WHERE table_name = 'roles' AND constraint_name LIKE '%pkey%'"))
        print('\nRoles primary key:')
        for row in result:
            print(f'  {row[0]}: {row[1]}')
        
        # Check system_users table structure
        result = await conn.execute(text("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'system_users' ORDER BY ordinal_position"))
        print('\nSystem_users table columns:')
        for row in result:
            print(f'  {row[0]}: {row[1]}')
        
        # Check system_users primary key
        result = await conn.execute(text("SELECT constraint_name, column_name FROM information_schema.key_column_usage WHERE table_name = 'system_users' AND constraint_name LIKE '%pkey%'"))
        print('\nSystem_users primary key:')
        for row in result:
            print(f'  {row[0]}: {row[1]}')

if __name__ == "__main__":
    asyncio.run(check_tables())
