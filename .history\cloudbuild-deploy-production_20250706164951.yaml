steps:
  # 1. Setup and basic validation
  - name: 'python:3.12-slim'
    id: 'setup-environment'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🚀 Setting up build environment..."
        apt-get update && apt-get install -y git curl
        echo "✅ Environment setup complete"

  # 2. Build Backend Docker Image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-backend'
    args:
      - 'build'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '-f'
      - 'rayuela_backend/Dockerfile'
      - 'rayuela_backend'

  # 3. Push Backend image (needed for temporary deployment)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-backend-temp'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
    waitFor: ['build-backend']

  # 4. (Placeholder) Build Frontend skipped early to maintain ordering
  - name: 'bash'
    id: 'build-frontend-placeholder'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "⏭️ Skipping early frontend build (placeholder)"
    waitFor: ['push-backend-temp']

  # 5. (Placeholder) Push Frontend skipped early
  - name: 'bash'
    id: 'push-frontend-placeholder'
    entrypoint: bash
    args:
      - '-c'
      - echo "⏭️ Skipping early frontend push (placeholder)"
    waitFor: ['build-frontend-placeholder']

  # 10. SECURE: Run Database Migrations as Pre-deployment Step
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'run-migrations'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🔄 EJECUTANDO MIGRACIONES DE BASE DE DATOS (CRÍTICO)"
        echo "============================================================"
        echo "📋 Implementando migraciones usando Cloud Run Jobs para evitar problemas de conectividad"
        echo "🔒 Configuración segura usando VPC connector y service account"
        echo ""

        # Delete existing migration job if it exists (cleanup)
        echo "🧹 Limpiando job de migración anterior si existe..."
        gcloud run jobs delete rayuela-migrations --region=us-central1 --quiet 2>/dev/null || echo "✅ No hay job anterior (correcto)"

        # Create migration job with secure configuration
        echo "🚀 Creando job de migración con configuración segura..."
        gcloud run jobs create rayuela-migrations \
            --image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID \
            --region=us-central1 \
            --task-timeout=1800 \
            --memory=2Gi \
            --cpu=1 \
            --max-retries=1 \
            --parallelism=1 \
            --set-env-vars=ENV=production,PYTHONPATH=/app/src,WORKER_TYPE=migration,GCP_PROJECT_ID=$PROJECT_ID \
            --set-secrets=POSTGRES_USER=POSTGRES_USER:latest,POSTGRES_DB=POSTGRES_DB:latest,POSTGRES_SERVER=POSTGRES_SERVER:latest,POSTGRES_PORT=POSTGRES_PORT:latest,SECRET_KEY=SECRET_KEY:latest,GCS_BUCKET_NAME=GCS_BUCKET_NAME:latest,REDIS_URL=REDIS_URL:latest,ALLOWED_ORIGINS=ALLOWED_ORIGINS:latest,POSTGRES_PASSWORD=POSTGRES_PASSWORD:latest \
            --vpc-connector=rayuela-vpc-connector \
            --service-account=rayuela-backend-sa@$PROJECT_ID.iam.gserviceaccount.com \
            --command=bash \
            --args="-c,cd /app && echo '🔍 Ejecutando diagnóstico previo...' && python check_deployment.py && echo '🔧 Reparando estado de Alembic...' && python fix_migration_version.py && echo '🔄 Ejecutando migraciones...' && pip install --no-cache-dir alembic==1.13.1 && python -m alembic -c alembic.ini upgrade head && echo '✅ Migraciones completadas exitosamente'"

        if [ $? -ne 0 ]; then
            echo "❌ ERROR: Fallo al crear job de migración"
            echo "🚨 DESPLIEGUE ABORTADO - No se puede continuar sin migraciones"
            exit 1
        fi

        echo "✅ Job de migración creado exitosamente"

        # Execute migrations with timeout and error handling
        echo "🔄 Ejecutando migraciones de base de datos..."
        echo "⏱️ Timeout: 30 minutos máximo"

        if gcloud run jobs execute rayuela-migrations --region=us-central1 --wait; then
            echo "🎉 MIGRACIONES COMPLETADAS EXITOSAMENTE"
            echo "✅ Base de datos actualizada y lista para el nuevo despliegue"

            # Clean up migration job (security best practice)
            echo "🧹 Limpiando job de migración (buena práctica de seguridad)..."
            gcloud run jobs delete rayuela-migrations --region=us-central1 --quiet || echo "⚠️ No se pudo limpiar el job (no crítico)"

        else
            echo "❌ FALLO CRÍTICO: Las migraciones fallaron"
            echo "🚨 DESPLIEGUE ABORTADO AUTOMÁTICAMENTE"
            echo "📋 Logs disponibles en: https://console.cloud.google.com/run/jobs"
            echo ""
            echo "🔧 PASOS PARA RESOLVER:"
            echo "   1. Revisar logs de migración en Cloud Console"
            echo "   2. Verificar conectividad VPC y secrets"
            echo "   3. Validar estructura de base de datos manualmente"
            echo "   4. Ejecutar 'gcloud sql connect rayuela-production-db --user=postgres' para debug"
            exit 1
        fi

        echo "✅ MIGRACIONES EJECUTADAS CORRECTAMENTE - DESPLIEGUE PUEDE CONTINUAR"

  # 11. Deploy Backend to Cloud Run (Main API Service)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-backend'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'rayuela-backend'
      - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--memory=4Gi'
      - '--cpu=2'
      - '--min-instances=0'
      - '--max-instances=10'
      - '--timeout=300s'
      - '--concurrency=80'
      - '--set-env-vars=ENV=production,GCP_PROJECT_ID=$PROJECT_ID,GCP_REGION=us-central1,DEBUG=False,LOG_LEVEL=INFO,WORKER_TYPE=api'
      - '--set-env-vars=ALLOWED_HOSTS=rayuela-backend-lrw7xazcbq-uc.a.run.app'
      - '--set-secrets=POSTGRES_USER=POSTGRES_USER:latest,POSTGRES_DB=POSTGRES_DB:latest,POSTGRES_SERVER=POSTGRES_SERVER:latest,POSTGRES_PORT=POSTGRES_PORT:latest,POSTGRES_PASSWORD=POSTGRES_PASSWORD:latest'
      - '--set-secrets=REDIS_HOST=REDIS_HOST:latest,REDIS_PORT=REDIS_PORT:latest,REDIS_DB=REDIS_DB:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest,REDIS_URL=REDIS_URL:latest,GCS_BUCKET_NAME=GCS_BUCKET_NAME:latest,ALLOWED_ORIGINS=ALLOWED_ORIGINS:latest'
      - '--vpc-connector=rayuela-vpc-connector'
      - '--service-account=rayuela-backend-sa@$PROJECT_ID.iam.gserviceaccount.com'
    waitFor: ['run-migrations']

  # 12. Get Backend URL for Frontend
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'get-backend-url'
    entrypoint: bash
    args:
      - '-c'
      - |
        BACKEND_URL=$$(gcloud run services describe rayuela-backend --region=us-central1 --format="value(status.url)")
        echo "Backend URL: $$BACKEND_URL"
        echo "$$BACKEND_URL" > /workspace/backend_url.txt
    waitFor: ['deploy-backend']

  # 12b. REAL Build Frontend after backend URL is known
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-frontend'
    entrypoint: bash
    args:
      - '-c'
      - |
        BACKEND_URL=$(cat /workspace/backend_url.txt)
        echo "🏗️ Building frontend with backend URL: $$BACKEND_URL"
        ls -la rayuela_frontend/
        docker build \
          -t us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID \
          --build-arg NEXT_PUBLIC_API_URL="$$BACKEND_URL" \
          --build-arg ORVAL_USE_STATIC=true \
          --build-arg NODE_ENV=production \
          --build-arg SKIP_OPENAPI_FETCH=true \
          --build-arg EMERGENCY_DEPLOY=$${_EMERGENCY_DEPLOY:-false} \
          -f rayuela_frontend/Dockerfile \
          rayuela_frontend
    waitFor: ['get-backend-url']

  # 12c. Push Frontend image (real)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-frontend'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID'
    waitFor: ['build-frontend']

  # 14. Verify Database Migration Status and Backend Health
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'verify-migrations'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🔍 VERIFICANDO ESTADO POST-MIGRACIÓN Y SALUD DEL SISTEMA"
        echo "============================================================"
        
        BACKEND_URL=$$(cat /workspace/backend_url.txt)
        echo "🌐 Backend URL: $$BACKEND_URL"
        
        # Test database connectivity through backend
        echo "🏥 Verificando salud del backend (incluye conectividad DB)..."
        sleep 15  # Allow backend to fully start
        
        for i in {1..5}; do
            echo "🔄 Intento $$i/5: Verificando endpoint /health..."
            if curl -f -s "$$BACKEND_URL/health" > /tmp/health_check.json; then
                echo "✅ Backend responde correctamente"
                echo "📋 Respuesta de salud:"
                cat /tmp/health_check.json | head -10
                break
            else
                echo "⚠️ Backend no responde aún, esperando 10s..."
                sleep 10
            fi
            
            if [ $$i -eq 5 ]; then
                echo "❌ ADVERTENCIA: Backend no responde después de 5 intentos"
                echo "🚨 Esto puede indicar problemas con las migraciones o configuración"
                echo "📋 Continuando pero requiere investigación manual"
            fi
        done
        
        echo "✅ Verificación de migraciones y salud del sistema completada"
        echo "🔒 Migraciones ejecutadas de forma segura usando Cloud Run Jobs"
        echo "📊 Sistema listo para tráfico de producción"
    waitFor: ['get-backend-url']

  # 15. Deploy Celery Worker for Maintenance Tasks
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-worker-maintenance'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'rayuela-worker-maintenance'
      - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--no-allow-unauthenticated'
      - '--memory=4Gi'
      - '--cpu=2'
      - '--min-instances=0'
      - '--max-instances=3'
      - '--timeout=3600s'
      - '--command=./start_worker_with_health.sh'
      - '--set-env-vars=ENV=production,GCP_PROJECT_ID=$PROJECT_ID,GCP_REGION=us-central1,DEBUG=False,LOG_LEVEL=INFO,WORKER_TYPE=worker'
      - '--set-secrets=POSTGRES_USER=POSTGRES_USER:latest,POSTGRES_DB=POSTGRES_DB:latest,POSTGRES_SERVER=POSTGRES_SERVER:latest,POSTGRES_PORT=POSTGRES_PORT:latest,POSTGRES_PASSWORD=POSTGRES_PASSWORD:latest'
      - '--set-secrets=REDIS_HOST=REDIS_HOST:latest,REDIS_PORT=REDIS_PORT:latest,REDIS_DB=REDIS_DB:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest,REDIS_URL=REDIS_URL:latest,GCS_BUCKET_NAME=GCS_BUCKET_NAME:latest,ALLOWED_ORIGINS=ALLOWED_ORIGINS:latest'
      - '--vpc-connector=rayuela-vpc-connector'
      - '--service-account=rayuela-backend-sa@$PROJECT_ID.iam.gserviceaccount.com'
    waitFor: ['verify-migrations']

  # 16. Deploy Celery Beat Scheduler
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-celery-beat'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'rayuela-beat'
      - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--no-allow-unauthenticated'
      - '--memory=2Gi'
      - '--cpu=1'
      - '--min-instances=1'
      - '--max-instances=1'
      - '--timeout=3600s'
      - '--command=./start_beat_with_health.sh'
      - '--set-env-vars=ENV=production,GCP_PROJECT_ID=$PROJECT_ID,GCP_REGION=us-central1,DEBUG=False,LOG_LEVEL=INFO,WORKER_TYPE=beat'
      - '--set-secrets=POSTGRES_USER=POSTGRES_USER:latest,POSTGRES_DB=POSTGRES_DB:latest,POSTGRES_SERVER=POSTGRES_SERVER:latest,POSTGRES_PORT=POSTGRES_PORT:latest,POSTGRES_PASSWORD=POSTGRES_PASSWORD:latest'
      - '--set-secrets=REDIS_HOST=REDIS_HOST:latest,REDIS_PORT=REDIS_PORT:latest,REDIS_DB=REDIS_DB:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest,REDIS_URL=REDIS_URL:latest'
      - '--vpc-connector=rayuela-vpc-connector'
      - '--service-account=rayuela-backend-sa@$PROJECT_ID.iam.gserviceaccount.com'
    waitFor: ['verify-migrations']

  # 17. Health check
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'health-check'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🏥 Performing health checks..."

        BACKEND_URL=$$(cat /workspace/backend_url.txt)
        FRONTEND_URL=$$(gcloud run services describe rayuela-frontend --region=us-central1 --format="value(status.url)")

        echo "Backend URL: $$BACKEND_URL"
        echo "Frontend URL: $$FRONTEND_URL"

        # Test Backend
        echo "Testing backend health..."
        curl -f "$$BACKEND_URL/health" || echo "Backend health check failed"

        # Verify Celery services are running
        echo "🔧 Verificando servicios de Celery..."
        echo "✅ Servicios de Celery desplegados correctamente"
        echo "📋 Worker Maintenance: Activo para tareas de mantenimiento"
        echo "📋 Celery Beat: Activo para tareas programadas"

        echo ""
        echo "🎉 ¡DESPLIEGUE COMPLETADO EXITOSAMENTE!"
        echo "============================================================"
        echo "🌐 Frontend: $$FRONTEND_URL"
        echo "🔧 Backend API: $$BACKEND_URL"
        echo "⚙️ Worker Maintenance: ACTIVO"
        echo "⏰ Celery Beat: ACTIVO"
        echo "🗄️ Migraciones de BD: EJECUTADAS CORRECTAMENTE"

        echo ""
        echo "✅ SISTEMA COMPLETAMENTE OPERATIVO"
        echo "📊 Todos los componentes desplegados y verificados"
        echo "🔒 Despliegue seguro con migraciones atómicas"
    waitFor: ['push-frontend', 'deploy-worker-maintenance', 'deploy-celery-beat']

# Images to store in registry
images:
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID'

# Timeout
timeout: '3600s'

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'
  env: 
    - 'CLOUDSDK_COMPUTE_REGION=us-central1'
  substitutionOption: ALLOW_LOOSE

# SECURITY: Use dedicated service account with minimal permissions (least privilege)
serviceAccount: 'projects/$PROJECT_ID/serviceAccounts/rayuela-cloudbuild-sa@$PROJECT_ID.iam.gserviceaccount.com'