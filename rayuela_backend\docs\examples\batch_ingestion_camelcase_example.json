{"users": [{"externalId": "user_001", "preferredCategories": ["electronics", "books", "clothing"], "dislikedCategories": ["sports"], "preferredBrands": ["Apple", "Samsung", "Nike"], "dislikedBrands": ["BrandX"], "priceRangeMin": 10.5, "priceRangeMax": 500.0, "demographicInfo": {"age": 28, "gender": "M", "location": "urban", "incomeLevel": "middle"}, "onboardingPreferences": {"shoppingFrequency": "weekly", "preferredShoppingTime": "evening", "interests": ["technology", "fashion", "reading"], "lifestyle": "active"}}, {"externalId": "user_002", "preferredCategories": ["home", "garden"], "priceRangeMin": 20.0, "priceRangeMax": 300.0, "demographicInfo": {"age": 35, "gender": "F", "location": "suburban"}}], "products": [{"externalId": "prod_001", "name": "Smartphone XYZ Pro", "description": "Un smartphone de última generación con cámara avanzada", "price": 599.99, "category": "electronics", "averageRating": 4.5, "numRatings": 120, "inventoryCount": 50}, {"externalId": "prod_002", "name": "Laptop ABC Professional", "description": "Laptop para profesionales con alto rendimiento", "price": 1299.99, "category": "electronics", "averageRating": 4.8, "numRatings": 85, "inventoryCount": 30}, {"externalId": "prod_003", "name": "Auriculares Bluetooth Premium", "description": "Auriculares inalámbricos con cancelación de ruido", "price": 89.99, "category": "electronics", "averageRating": 4.2, "numRatings": 210, "inventoryCount": 75}], "interactions": [{"userId": 1, "productId": 1, "interactionType": "VIEW", "value": 1.0, "recommendationMetadata": {"source": "homepage", "algorithm": "collaborative_filtering", "confidence": 0.85}}, {"userId": 1, "productId": 1, "interactionType": "PURCHASE", "value": 1.0, "recommendationMetadata": {"source": "product_page", "algorithm": "content_based", "confidence": 0.92}}, {"userId": 2, "productId": 2, "interactionType": "VIEW", "value": 1.0, "recommendationMetadata": {"source": "search_results", "algorithm": "hybrid", "confidence": 0.78}}, {"userId": 2, "productId": 3, "interactionType": "RATING", "value": 4.5, "recommendationMetadata": {"source": "recommendation_widget", "algorithm": "collaborative_filtering", "confidence": 0.88}}]}