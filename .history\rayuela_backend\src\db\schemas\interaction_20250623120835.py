from pydantic import BaseModel, <PERSON>
from datetime import datetime
from typing import Optional, Dict, Any, Union
from src.db.enums import InteractionType


class InteractionBase(BaseModel):
    user_id: int
    product_id: int
    interaction_type: InteractionType
    value: float
    recommendation_metadata: Optional[Dict[str, Any]] = None


class InteractionExternalCreate(BaseModel):
    """Schema for creating interactions using external IDs."""
    external_user_id: str = Field(..., description="External user identifier provided by the client")
    external_product_id: str = Field(..., description="External product identifier provided by the client")
    interaction_type: InteractionType
    value: float
    recommendation_metadata: Optional[Dict[str, Any]] = None


class InteractionCreate(InteractionBase):
    pass


class Interaction(InteractionBase):
    id: int
    account_id: int
    timestamp: datetime

    class ConfigDict:
        from_attributes = True


class InteractionExternal(BaseModel):
    """Schema for interaction responses that include external IDs."""
    id: int
    account_id: int
    external_user_id: str = Field(..., description="External user identifier")
    external_product_id: str = Field(..., description="External product identifier")
    interaction_type: InteractionType
    value: float
    timestamp: datetime
    recommendation_metadata: Optional[Dict[str, Any]] = None

    class ConfigDict:
        from_attributes = True
