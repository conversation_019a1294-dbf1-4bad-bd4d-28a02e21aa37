from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime
from src.db.enums import TrainingJobStatus


class SearchBase(BaseModel):
    end_user_id: Optional[int] = None  # Match database schema
    query: str
    filters: Optional[Dict[str, Any]] = None  # Match database schema
    results_count: Optional[int] = None  # Match database schema
    session_id: Optional[str] = None  # Match database schema


class SearchCreate(SearchBase):
    pass


class Search(SearchBase):
    id: int
    account_id: int
    timestamp: datetime

    class ConfigDict:
        from_attributes = True
