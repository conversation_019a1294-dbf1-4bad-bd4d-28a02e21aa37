#!/usr/bin/env python3
"""
<PERSON>ript to check the current migration status and identify problematic migrations.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path for imports
current_dir = Path(__file__).parent.parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

# Set PYTHONPATH environment variable
os.environ['PYTHONPATH'] = str(src_dir)

try:
    from db.database import get_database_manager
    from core.config import settings
except ImportError as e:
    print(f"❌ Import error: {e}")
    print(f"Current directory: {current_dir}")
    print(f"Source directory: {src_dir}")
    print(f"Source directory exists: {src_dir.exists()}")
    if src_dir.exists():
        print(f"Contents: {list(src_dir.iterdir())}")
    sys.exit(1)

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def check_migration_status():
    """Check current migration status and identify issues."""
    logger.info("🔍 Checking migration status...")
    
    db_manager = get_database_manager()
    
    try:
        async with db_manager.get_session() as session:
            # Check current Alembic version
            result = await session.execute("""
                SELECT version_num 
                FROM alembic_version 
                ORDER BY version_num DESC 
                LIMIT 1
            """)
            current_version = result.scalar()
            logger.info(f"📊 Current Alembic version: {current_version}")
            
            # Check if interactions table exists and has IDENTITY
            result = await session.execute("""
                SELECT 
                    column_name,
                    data_type,
                    is_nullable,
                    column_default,
                    is_identity,
                    identity_generation
                FROM information_schema.columns 
                WHERE table_name = 'interactions' 
                AND table_schema = 'public'
                ORDER BY ordinal_position
            """)
            
            interactions_columns = result.fetchall()
            
            if interactions_columns:
                logger.info("📋 Interactions table structure:")
                for col in interactions_columns:
                    identity_info = f" (IDENTITY: {col.is_identity})" if col.is_identity == 'YES' else ""
                    logger.info(f"  - {col.column_name}: {col.data_type}{identity_info}")
                    
                # Check specifically for id column IDENTITY status
                id_column = next((col for col in interactions_columns if col.column_name == 'id'), None)
                if id_column:
                    if id_column.is_identity == 'YES':
                        logger.info("✅ interactions.id already has IDENTITY property")
                        return True, "interactions.id already has IDENTITY"
                    else:
                        logger.info("❌ interactions.id does NOT have IDENTITY property")
                        return False, "interactions.id missing IDENTITY"
                else:
                    logger.error("❌ interactions.id column not found!")
                    return False, "interactions.id column missing"
            else:
                logger.error("❌ interactions table not found!")
                return False, "interactions table missing"
                
    except Exception as e:
        logger.error(f"❌ Error checking migration status: {e}")
        return False, f"Error: {e}"


async def check_alembic_history():
    """Check Alembic migration history."""
    logger.info("📚 Checking Alembic migration history...")
    
    db_manager = get_database_manager()
    
    try:
        async with db_manager.get_session() as session:
            # Get all applied migrations
            result = await session.execute("""
                SELECT version_num 
                FROM alembic_version 
                ORDER BY version_num
            """)
            applied_versions = [row[0] for row in result.fetchall()]
            
            logger.info(f"📊 Applied migrations ({len(applied_versions)}):")
            for version in applied_versions:
                logger.info(f"  ✅ {version}")
                
            # Check if the problematic migration is applied
            problematic_migration = "7fc3e61b6b94"
            if problematic_migration in applied_versions:
                logger.warning(f"⚠️ Problematic migration {problematic_migration} is already applied!")
                return True, "problematic migration applied"
            else:
                logger.info(f"ℹ️ Problematic migration {problematic_migration} not yet applied")
                return False, "problematic migration not applied"
                
    except Exception as e:
        logger.error(f"❌ Error checking Alembic history: {e}")
        return False, f"Error: {e}"


async def main():
    """Main function to run all checks."""
    logger.info("🚀 Starting migration status check...")
    
    # Check migration status
    status_ok, status_msg = await check_migration_status()
    
    # Check Alembic history
    history_ok, history_msg = await check_alembic_history()
    
    logger.info("\n" + "="*50)
    logger.info("📊 MIGRATION STATUS SUMMARY")
    logger.info("="*50)
    logger.info(f"Database Status: {'✅ OK' if status_ok else '❌ ISSUE'} - {status_msg}")
    logger.info(f"Migration History: {'✅ OK' if history_ok else '❌ ISSUE'} - {history_msg}")
    
    if status_ok and not history_ok:
        logger.info("\n💡 RECOMMENDATION:")
        logger.info("The interactions.id column already has IDENTITY, but the migration hasn't been applied.")
        logger.info("This suggests the column was created with IDENTITY in a previous migration.")
        logger.info("The fixed migration should now work correctly.")
    elif not status_ok and not history_ok:
        logger.info("\n💡 RECOMMENDATION:")
        logger.info("The migration needs to be applied to add IDENTITY to interactions.id")
    elif status_ok and history_ok:
        logger.warning("\n⚠️ WARNING:")
        logger.warning("The problematic migration was already applied, but the column has IDENTITY.")
        logger.warning("This suggests the migration ran successfully despite the error.")
    
    return status_ok or not history_ok


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
