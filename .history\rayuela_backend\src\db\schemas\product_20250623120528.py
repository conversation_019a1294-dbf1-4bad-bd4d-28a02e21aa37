from pydantic import BaseModel, Field, condecimal
from typing import Optional
from datetime import datetime
from decimal import Decimal


class ProductBase(BaseModel):
    external_id: str = Field(..., min_length=1, max_length=255, description="External identifier provided by the client (e.g., SKU, product code)")
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None  # Ya no hay límite de longitud
    price: Decimal = Field(..., gt=0, decimal_places=2, max_digits=10)
    category: str = Field(..., min_length=1, max_length=100)
    average_rating: float = Field(0.0, ge=0, le=5)
    num_ratings: int = Field(0, ge=0)
    inventory_count: int = Field(0, ge=0)

    # @field_validator("category")
    # def validate_category(cls, v):
    #     allowed_categories = {
    #         "electronics",
    #         "books",
    #         "clothing",
    #     }  # Definir categorías permitidas
    #     if v.lower() not in allowed_categories:
    #         raise ValueError(f"Category must be one of: {allowed_categories}")
    #     return v.lower()


class ProductCreate(ProductBase):
    pass


class ProductUpdate(BaseModel):
    external_id: Optional[str] = Field(None, min_length=1, max_length=255, description="External identifier provided by the client (e.g., SKU, product code)")
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None  # Ya no hay límite de longitud
    price: Optional[Decimal] = Field(None, gt=0, decimal_places=2, max_digits=10)
    category: Optional[str] = Field(None, min_length=1, max_length=100)
    average_rating: Optional[float] = Field(None, ge=0, le=5)
    num_ratings: Optional[int] = Field(None, ge=0)
    inventory_count: Optional[int] = Field(None, ge=0)


class Product(ProductBase):
    product_id: int
    account_id: int
    created_at: datetime
    updated_at: datetime
    is_active: bool = True
    deleted_at: Optional[datetime] = None
    score: Optional[float] = None
    source: Optional[str] = None
    explanation: Optional[str] = None  # Campo para explicar la razón de la recomendación

    class ConfigDict:
        from_attributes = True


class Category(BaseModel):
    """Categoría de producto"""

    name: str = Field(..., min_length=1, max_length=100)
    product_count: Optional[int] = 0
    popularity: Optional[float] = 0.0


class InventoryUpdate(BaseModel):
    inventory_count: int = Field(
        ..., ge=0, description="Cantidad de inventario disponible"
    )

    # @field_validator("inventory_count")
    # def inventory_must_be_non_negative(cls, v):
    #     if v < 0:
    #         raise ValueError("Inventory count must be non-negative")
    #     return v
