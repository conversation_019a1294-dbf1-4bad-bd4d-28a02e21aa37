"use client";

import { useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  ChartOptions,
  ChartData,
} from 'chart.js';
import { Bar, Radar } from 'react-chartjs-2';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { TooltipHelper } from '@/components/ui/tooltip-helper';
import { InfoIcon } from 'lucide-react';
import { RecommendationPerformanceMetrics } from '@/lib/api/recommendation-metrics';
import {
  chartColors,
  getColorByIndex,
  formatPercentage
} from '@/lib/chart-utils';
import { formatNumber } from '@/lib/utils/format';
import { Badge } from '@/components/ui/badge';

// Registrar los componentes necesarios de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend
);

// Type for model data with safe property access
interface ModelData {
  model_type: string;
  version: string;
  metrics?: {
    precision?: number;
    recall?: number;
    ndcg?: number;
    map?: number;
    catalog_coverage?: number;
    diversity?: number;
    novelty?: number;
    serendipity?: number;
  };
}

// Type for resource metrics with safe property access
interface ResourceMetricValue {
  avg_value?: number;
  max_value?: number;
}

interface ResourceMetrics {
  memory_usage_mb?: ResourceMetricValue;
  cpu_percent?: ResourceMetricValue;
  evaluation_time_seconds?: ResourceMetricValue;
  collab_training_time?: ResourceMetricValue;
  content_training_time?: ResourceMetricValue;
}

interface RecommendationMetricsChartProps {
  data?: RecommendationPerformanceMetrics;
  isLoading?: boolean;
  error?: Error | null;
  title?: string;
  description?: string;
}

export default function RecommendationMetricsChart({
  data,
  isLoading = false,
  error = null,
  title = "Métricas de Recomendación",
  description = "Rendimiento de los modelos de recomendación"
}: RecommendationMetricsChartProps) {
  const [activeTab, setActiveTab] = useState('accuracy');

  // Type guard to safely access data properties
  const safeData = data && typeof data === 'object' && 'models' in data ? data as RecommendationPerformanceMetrics : null;
  const models = safeData?.models && Array.isArray(safeData.models) ? safeData.models as ModelData[] : [];

  // Datos para el gráfico de precisión/recall
  const accuracyData: ChartData<'bar'> = {
    labels: models.map((model: ModelData) => `${model.model_type} (${model.version})`) || [],
    datasets: [
      {
        label: 'Precisión',
        data: models.map((model: ModelData) => model.metrics?.precision ? model.metrics.precision * 100 : 0) || [],
        backgroundColor: chartColors.blue.background,
        borderColor: chartColors.blue.primary,
        borderWidth: 1,
      },
      {
        label: 'Recall',
        data: models.map((model: ModelData) => model.metrics?.recall ? model.metrics.recall * 100 : 0) || [],
        backgroundColor: chartColors.green.background,
        borderColor: chartColors.green.primary,
        borderWidth: 1,
      },
      {
        label: 'NDCG',
        data: models.map((model: ModelData) => model.metrics?.ndcg ? model.metrics.ndcg * 100 : 0) || [],
        backgroundColor: chartColors.orange.background,
        borderColor: chartColors.orange.primary,
        borderWidth: 1,
      },
      {
        label: 'MAP',
        data: models.map((model: ModelData) => model.metrics?.map ? model.metrics.map * 100 : 0) || [],
        backgroundColor: chartColors.purple.background,
        borderColor: chartColors.purple.primary,
        borderWidth: 1,
      },
    ],
  };

  // Datos para el gráfico de diversidad/novedad
  const diversityData: ChartData<'bar'> = {
    labels: models.map((model: ModelData) => `${model.model_type} (${model.version})`) || [],
    datasets: [
      {
        label: 'Cobertura del Catálogo',
        data: models.map((model: ModelData) => model.metrics?.catalog_coverage ? model.metrics.catalog_coverage * 100 : 0) || [],
        backgroundColor: chartColors.blue.background,
        borderColor: chartColors.blue.primary,
        borderWidth: 1,
      },
      {
        label: 'Diversidad',
        data: models.map((model: ModelData) => model.metrics?.diversity ? model.metrics.diversity * 100 : 0) || [],
        backgroundColor: chartColors.green.background,
        borderColor: chartColors.green.primary,
        borderWidth: 1,
      },
      {
        label: 'Novedad',
        data: models.map((model: ModelData) => model.metrics?.novelty ? model.metrics.novelty * 100 : 0) || [],
        backgroundColor: chartColors.orange.background,
        borderColor: chartColors.orange.primary,
        borderWidth: 1,
      },
      {
        label: 'Serendipia',
        data: models.map((model: ModelData) => model.metrics?.serendipity ? model.metrics.serendipity * 100 : 0) || [],
        backgroundColor: chartColors.purple.background,
        borderColor: chartColors.purple.primary,
        borderWidth: 1,
      },
    ],
  };

  // Datos para el gráfico de radar (comparación de modelos)
  const radarData: ChartData<'radar'> = {
    labels: ['Precisión', 'Recall', 'NDCG', 'MAP', 'Cobertura', 'Diversidad', 'Novedad', 'Serendipia'],
    datasets: models.map((model: ModelData, index: number) => {
      const color = getColorByIndex(index);
      return {
        label: `${model.model_type} (${model.version})`,
        data: [
          model.metrics?.precision ? model.metrics.precision * 100 : 0,
          model.metrics?.recall ? model.metrics.recall * 100 : 0,
          model.metrics?.ndcg ? model.metrics.ndcg * 100 : 0,
          model.metrics?.map ? model.metrics.map * 100 : 0,
          model.metrics?.catalog_coverage ? model.metrics.catalog_coverage * 100 : 0,
          model.metrics?.diversity ? model.metrics.diversity * 100 : 0,
          model.metrics?.novelty ? model.metrics.novelty * 100 : 0,
          model.metrics?.serendipity ? model.metrics.serendipity * 100 : 0,
        ],
        backgroundColor: color.background,
        borderColor: color.primary,
        borderWidth: 1,
      };
    }) || [],
  };

  // Safe access to resource metrics
  const resourceMetrics = safeData?.resource_metrics && typeof safeData.resource_metrics === 'object' ? safeData.resource_metrics as ResourceMetrics : null;

  // Datos para el gráfico de recursos
  const resourceData: ChartData<'bar'> = {
    labels: ['Uso de Memoria (MB)', 'CPU (%)', 'Tiempo de Evaluación (s)', 'Tiempo Entrenamiento Colaborativo (s)', 'Tiempo Entrenamiento Contenido (s)'],
    datasets: [
      {
        label: 'Promedio',
        data: resourceMetrics ? [
          resourceMetrics.memory_usage_mb?.avg_value || 0,
          resourceMetrics.cpu_percent?.avg_value || 0,
          resourceMetrics.evaluation_time_seconds?.avg_value || 0,
          resourceMetrics.collab_training_time?.avg_value || 0,
          resourceMetrics.content_training_time?.avg_value || 0,
        ] : [],
        backgroundColor: chartColors.blue.background,
        borderColor: chartColors.blue.primary,
        borderWidth: 1,
      },
      {
        label: 'Máximo',
        data: resourceMetrics ? [
          resourceMetrics.memory_usage_mb?.max_value || 0,
          resourceMetrics.cpu_percent?.max_value || 0,
          resourceMetrics.evaluation_time_seconds?.max_value || 0,
          resourceMetrics.collab_training_time?.max_value || 0,
          resourceMetrics.content_training_time?.max_value || 0,
        ] : [],
        backgroundColor: chartColors.orange.background,
        borderColor: chartColors.orange.primary,
        borderWidth: 1,
      },
    ],
  };

  // Opciones comunes para los gráficos
  const barOptions: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += formatPercentage(context.parsed.y);
            }
            return label;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function (value) {
            return formatPercentage(value as number, 0);
          }
        }
      }
    },
  };

  const resourceOptions: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y' as const,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.x !== null) {
              const value = context.parsed.x;
              const formattedValue = context.dataIndex <= 1
                ? formatPercentage(value, 1) // For CPU percentage
                : formatNumber(value); // For time values
              return `${label}${formattedValue}`;
            }
            return label;
          }
        }
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        ticks: {
          callback: function (value, index) {
            // Format based on the metric type
            if (index <= 1) { // CPU percentage
              return formatPercentage(value as number, 0);
            }
            return formatNumber(value as number);
          }
        }
      }
    },
  };

  const radarOptions: ChartOptions<'radar'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.raw !== null) {
              label += formatPercentage(context.raw as number);
            }
            return label;
          }
        }
      }
    },
    scales: {
      r: {
        min: 0,
        max: 100,
        ticks: {
          stepSize: 20,
          callback: function (value) {
            return formatPercentage(value as number, 0);
          }
        },
        pointLabels: {
          font: {
            size: 10
          }
        }
      }
    },
  };

  if (isLoading) {
    return (
      <Card className="transition-all duration-300 hover:shadow-md">
        <CardHeader>
          <CardTitle><Skeleton className="h-6 w-1/3" /></CardTitle>
          <CardDescription><Skeleton className="h-4 w-1/2" /></CardDescription>
          <Skeleton className="h-10 w-full mt-2" />
        </CardHeader>
        <CardContent className="h-80">
          <Skeleton className="h-full w-full rounded-md" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="transition-all duration-300 hover:shadow-md border-red-200 dark:border-red-800">
        <CardHeader>
          <CardTitle className="text-red-500 flex items-center gap-2">
            <InfoIcon className="h-5 w-5" />
            {title} - Error
          </CardTitle>
          <CardDescription className="text-red-400">
            No se pudieron cargar las métricas: {error.message}
          </CardDescription>
        </CardHeader>
        <CardContent className="h-80">
          <div className="flex flex-col items-center justify-center h-full gap-2">
            <p className="text-red-500 mb-2">Ocurrió un error al cargar los datos de métricas.</p>
            <p className="text-sm text-gray-500">Intenta de nuevo más tarde o contacta con soporte.</p>
            <Badge variant="outline" className="mt-2 bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800">
              Error: {error.message}
            </Badge>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!safeData || models.length === 0) {
    return (
      <Card className="transition-all duration-300 hover:shadow-md">
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent className="h-80">
          <div className="flex flex-col items-center justify-center h-full gap-2">
            <InfoIcon className="h-12 w-12 text-gray-300 mb-2" />
            <p className="text-gray-500 font-medium">No hay datos disponibles</p>
            <p className="text-sm text-gray-400 text-center max-w-md">
              Entrena un modelo para ver métricas de rendimiento y comparar diferentes algoritmos de recomendación.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="transition-all duration-300 hover:shadow-md">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl font-semibold">{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>

          {/* Información de modelos */}
          {models.length > 0 && (
            <Badge variant="outline" className="bg-info-light text-info border-info/30">
              {models.length} {models.length === 1 ? 'modelo' : 'modelos'} analizados
            </Badge>
          )}
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-2">
          <TabsList className="grid grid-cols-4 w-full">
            <div className="relative">
              <TabsTrigger
                value="accuracy"
                className="transition-all duration-200 data-[state=active]:bg-blue-100 data-[state=active]:text-blue-700 dark:data-[state=active]:bg-blue-900/30 dark:data-[state=active]:text-blue-300 w-full"
              >
                <div className="flex items-center justify-center gap-1">
                  Precisión
                  <TooltipHelper
                    content={
                      <div className="space-y-2">
                        <p className="text-gray-700 dark:text-gray-300">
                          Métricas que evalúan la relevancia y calidad del ranking de las recomendaciones.
                        </p>
                        <ul className="list-disc pl-4 text-gray-700 dark:text-gray-300">
                          <li><span className="font-medium">Precisión:</span> Proporción de recomendaciones relevantes</li>
                          <li><span className="font-medium">Recall:</span> Proporción de ítems relevantes recomendados</li>
                          <li><span className="font-medium">NDCG:</span> Calidad del ranking considerando posiciones</li>
                          <li><span className="font-medium">MAP:</span> Precisión media en diferentes puntos de recall</li>
                        </ul>
                      </div>
                    }
                    iconSize={12}
                  />
                </div>
              </TabsTrigger>
            </div>
            <div className="relative">
              <TabsTrigger
                value="diversity"
                className="transition-all duration-200 data-[state=active]:bg-green-100 data-[state=active]:text-green-700 dark:data-[state=active]:bg-green-900/30 dark:data-[state=active]:text-green-300 w-full"
              >
                <div className="flex items-center justify-center gap-1">
                  Diversidad
                  <TooltipHelper
                    content={
                      <div className="space-y-2">
                        <p className="text-gray-700 dark:text-gray-300">
                          Métricas que evalúan la variedad y originalidad de las recomendaciones.
                        </p>
                        <ul className="list-disc pl-4 text-gray-700 dark:text-gray-300">
                          <li><span className="font-medium">Cobertura del Catálogo:</span> Porcentaje del catálogo recomendado</li>
                          <li><span className="font-medium">Diversidad:</span> Variedad en categorías y atributos</li>
                          <li><span className="font-medium">Novedad:</span> Recomendación de ítems poco populares</li>
                          <li><span className="font-medium">Serendipia:</span> Recomendaciones relevantes pero sorprendentes</li>
                        </ul>
                      </div>
                    }
                    iconSize={12}
                  />
                </div>
              </TabsTrigger>
            </div>
            <div className="relative">
              <TabsTrigger
                value="comparison"
                className="transition-all duration-200 data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700 dark:data-[state=active]:bg-purple-900/30 dark:data-[state=active]:text-purple-300 w-full"
              >
                <div className="flex items-center justify-center gap-1">
                  Comparación
                  <TooltipHelper
                    content={
                      <div className="space-y-2">
                        <p className="text-gray-700 dark:text-gray-300">
                          Comparación visual de todos los modelos en todas las métricas clave.
                        </p>
                        <p className="text-gray-700 dark:text-gray-300">
                          El gráfico de radar permite identificar rápidamente:
                        </p>
                        <ul className="list-disc pl-4 text-gray-700 dark:text-gray-300">
                          <li>Fortalezas y debilidades de cada modelo</li>
                          <li>Diferencias de rendimiento entre modelos</li>
                          <li>Áreas de mejora potencial</li>
                        </ul>
                      </div>
                    }
                    iconSize={12}
                  />
                </div>
              </TabsTrigger>
            </div>
            <div className="relative">
              <TabsTrigger
                value="resources"
                className="transition-all duration-200 data-[state=active]:bg-orange-100 data-[state=active]:text-orange-700 dark:data-[state=active]:bg-orange-900/30 dark:data-[state=active]:text-orange-300 w-full"
              >
                <div className="flex items-center justify-center gap-1">
                  Recursos
                  <TooltipHelper
                    content={
                      <div className="space-y-2">
                        <p className="text-gray-700 dark:text-gray-300">
                          Métricas de recursos computacionales utilizados por los modelos.
                        </p>
                        <ul className="list-disc pl-4 text-gray-700 dark:text-gray-300">
                          <li><span className="font-medium">Uso de Memoria:</span> RAM utilizada durante el entrenamiento/inferencia</li>
                          <li><span className="font-medium">CPU:</span> Porcentaje de utilización de CPU</li>
                          <li><span className="font-medium">Tiempo de Evaluación:</span> Tiempo para evaluar el modelo</li>
                          <li><span className="font-medium">Tiempo de Entrenamiento:</span> Tiempo para entrenar los modelos</li>
                        </ul>
                      </div>
                    }
                    iconSize={12}
                  />
                </div>
              </TabsTrigger>
            </div>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent className="h-80 pt-4">
        <TabsContent value="accuracy" className="h-full mt-0">
          <Bar
            options={barOptions}
            data={accuracyData}
            className="h-full"
          />
        </TabsContent>
        <TabsContent value="diversity" className="h-full mt-0">
          <Bar
            options={barOptions}
            data={diversityData}
            className="h-full"
          />
        </TabsContent>
        <TabsContent value="comparison" className="h-full mt-0">
          <Radar
            options={radarOptions}
            data={radarData}
            className="h-full"
          />
        </TabsContent>
        <TabsContent value="resources" className="h-full mt-0">
          <Bar
            options={resourceOptions}
            data={resourceData}
            className="h-full"
          />
        </TabsContent>
      </CardContent>
    </Card>
  );
}
