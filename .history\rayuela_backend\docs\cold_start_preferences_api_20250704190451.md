# Enhanced Cold Start Recommendations with User Preferences

## Overview

This enhancement adds support for collecting and utilizing user preferences during the onboarding process to provide more relevant recommendations for new users, addressing the cold start problem in recommendation systems.

## New Features

### 1. Enhanced EndUser Model

The `EndUser` model now includes the following preference fields:

- `preferred_categories`: List of preferred product categories
- `disliked_categories`: List of categories to avoid
- `preferred_brands`: List of preferred brands
- `disliked_brands`: List of brands to avoid
- `price_range_min`: Minimum price preference
- `price_range_max`: Maximum price preference
- `demographic_info`: Demographic information (JSON)
- `onboarding_preferences`: Additional onboarding preferences (JSON)

### 2. Enhanced User Creation API

The `POST /api/v1/end-users` endpoint now accepts optional preference fields:

```json
{
  "external_id": "user_12345",
  "preferred_categories": ["electronics", "books", "clothing"],
  "disliked_categories": ["sports"],
  "preferred_brands": ["Apple", "Nike", "Samsung"],
  "disliked_brands": ["BrandX"],
  "price_range_min": 10,
  "price_range_max": 500,
  "demographic_info": {
    "age_group": "25-34",
    "gender": "female",
    "location": "urban",
    "income_level": "middle"
  },
  "onboarding_preferences": {
    "shopping_frequency": "weekly",
    "preferred_shopping_time": "evening",
    "interests": ["technology", "fashion", "reading"],
    "lifestyle": "active"
  }
}
```

### 3. Preference-Based Fallback Strategy

A new fallback strategy `_get_preference_based_recommendations` has been added that:

- Filters products based on preferred/disliked categories
- Applies price range filters
- Scores products based on preference matching
- Provides detailed explanation of why products were recommended

## API Usage Examples

### Creating a User with Preferences

```bash
curl -X POST "https://api.rayuela.ai/api/v1/end-users" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "external_id": "new_user_001",
    "preferred_categories": ["electronics", "books"],
    "price_range_min": 25,
    "price_range_max": 300,
    "demographic_info": {
      "age_group": "18-24",
      "location": "urban"
    }
  }'
```

### Getting Recommendations for New Users

When requesting recommendations for a new user with preferences, the system will:

1. **First Priority**: Use preference-based recommendations
2. **Second Priority**: Use segment-based recommendations (if segment available)
3. **Fallback**: Use popular, trending, and new arrival strategies

```bash
curl -X POST "https://api.rayuela.ai/api/v1/recommendations" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 123,
    "limit": 10,
    "strategy": "hybrid"
  }'
```

## Response Format

Preference-based recommendations include additional metadata:

```json
{
  "items": [
    {
      "item_id": 456,
      "name": "iPhone 15",
      "category": "electronics",
      "price": 299,
      "score": 0.95,
      "model_type": "fallback",
      "fallback_reason": "onboarding_preferences",
      "matched_preferences": {
        "category_match": true,
        "price_in_range": true
      },
      "rank": 1
    }
  ]
}
```

## Implementation Benefits

### 1. Improved Cold Start Experience
- New users receive relevant recommendations immediately
- Reduces the "empty state" problem for new accounts
- Increases user engagement from the first session

### 2. Better Personalization
- Leverages explicit user preferences
- Combines with implicit behavior data as it becomes available
- Provides transparency in recommendation reasoning

### 3. Flexible Preference Collection
- Support for various preference types (categories, brands, price, demographics)
- Extensible JSON fields for custom attributes
- Optional fields allow gradual preference collection

## Database Schema Changes

The migration `add_user_preferences_cold_start` adds:

```sql
-- New columns in end_users table
ALTER TABLE end_users ADD COLUMN preferred_categories JSON;
ALTER TABLE end_users ADD COLUMN disliked_categories JSON;
ALTER TABLE end_users ADD COLUMN preferred_brands JSON;
ALTER TABLE end_users ADD COLUMN disliked_brands JSON;
ALTER TABLE end_users ADD COLUMN price_range_min INTEGER;
ALTER TABLE end_users ADD COLUMN price_range_max INTEGER;
ALTER TABLE end_users ADD COLUMN demographic_info JSON;
ALTER TABLE end_users ADD COLUMN onboarding_preferences JSON;

-- Performance indexes
CREATE INDEX idx_end_user_preferred_categories ON end_users USING gin (account_id, preferred_categories);
CREATE INDEX idx_end_user_price_range ON end_users (account_id, price_range_min, price_range_max);
```

## Testing

Run the test suite to verify the implementation:

```bash
pytest tests/test_cold_start_preferences.py -v
```

## Future Enhancements

1. **Machine Learning Integration**: Use preference data to train personalized models
2. **Preference Learning**: Automatically infer preferences from user behavior
3. **A/B Testing**: Compare preference-based vs. traditional cold start strategies
4. **Advanced Filtering**: Support for complex preference rules and combinations

## Migration Guide

To apply the database changes:

```bash
cd rayuela_backend
alembic upgrade head
```

The changes are backward compatible - existing users without preferences will continue to receive recommendations using the existing fallback strategies.
