import { useState, useEffect } from 'react';
import { getRayuela, type TrainingJobStatus } from '@/lib/generated/rayuelaAPI';

// Extend generated schema with extra UI helper fields
export type TrainingJob = TrainingJobStatus & {
  /** Nombre legible del modelo (derivado de model.artifact_name) */
  model_name: string;
  /** Versión legible del modelo (derivado de model.artifact_version) */
  model_version: string;
  duration?: number;
  parameters?: Record<string, number | string>;
  metrics?: Record<string, number>;
};

export function useTrainingJobs() {
  const [jobs, setJobs] = useState<TrainingJob[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchJobs = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Fetch recent training jobs via backend listing endpoint
      try {
        const response = await getRayuela().listTrainingJobsApiV1PipelineJobsGet();
        const jobsList = response.data;
        
        const jobsData: TrainingJob[] = jobsList.map((apiJobStatus) => {
          const job: TrainingJob = {
            ...apiJobStatus,
            model_name: apiJobStatus.model?.artifact_name ?? 'Recommendation Model',
            model_version: apiJobStatus.model?.artifact_version ?? 'v1.0',
            status: apiJobStatus.status.toUpperCase() as 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED',
            parameters: apiJobStatus.parameters ? (
              Object.fromEntries(
                Object.entries(apiJobStatus.parameters as Record<string, unknown>).filter(([, value]) =>
                  typeof value === 'number' || typeof value === 'string'
                )
              ) as Record<string, number | string>
            ) : undefined,
            metrics: apiJobStatus.metrics ? (
              Object.fromEntries(
                Object.entries(apiJobStatus.metrics as Record<string, unknown>).filter(([, value]) =>
                  typeof value === 'number'
                )
              ) as Record<string, number>
            ) : undefined,
          };
          
          // Calculate duration if both startedAt and completedAt are available
          if (job.startedAt && job.completedAt) {
            const startTime = new Date(job.startedAt).getTime();
            const endTime = new Date(job.completedAt).getTime();
            job.duration = Math.round((endTime - startTime) / 1000); // in seconds
          }
          
          return job;
        });
        
        setJobs(jobsData);
        return; // Exit early if successful
      } catch (listingError) {
        setError('Error fetching training jobs');
        console.error('Error fetching training jobs:', listingError);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error loading training jobs');
      console.error('Error loading training jobs:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const getJobStatus = async (jobId: number) => {
    try {
      const response = await getRayuela().getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet(jobId);
      return response.data;
    } catch (err) {
      console.error('Error fetching training job status:', err);
      throw err;
    }
  };

  const startTraining = async (parameters?: Record<string, unknown>) => {
    try {
      const response = await getRayuela().trainModelsApiV1PipelineTrainPost();
      
      // Refresh jobs list directly
      await fetchJobs();
      return response.data;
    } catch (err) {
      console.error('Error starting training:', err);
      throw err;
    }
  };

  useEffect(() => {
    fetchJobs();
  }, []);

  return {
    jobs,
    isLoading,
    error,
    fetchJobs,
    getJobStatus,
    startTraining
  };
} 