// src/app/(public)/home/<USER>
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { generateMetadata as generateSEOMetadata, generateJsonLd } from '@/lib/seo';
import { IconText } from '@/components/ui/spacing-system';
// Importing directly from public via absolute path avoids webpack alias issues in production builds
const LOGO_PATH = '/name_logo.png';
const CONNECTION_PATH = '/productos_personas.png';

export const metadata = generateSEOMetadata({
  title: 'Rayuela.ai: Recomendaciones API-first para Marketplaces',
  description:
    'Ofrecé recomendaciones personalizadas en tu e-commerce con una plataforma B2B API-first. Fácil de integrar, escalable y con IA de vanguardia.',
  path: '/',
  keywords: [
    'recomendaciones e-commerce',
    'API inteligencia artificial',
    'machine learning retail',
    'personalización B2B',
    'Rayuela.ai',
  ],
});

export default function HomePage() {
  const organizationSchema = generateJsonLd('Organization', {});
  const softwareSchema = generateJsonLd('SoftwareApplication', {
    name: 'Rayuela.ai',
    description:
      'Plataforma API-first de personalización inteligente para e-commerce y marketplaces',
  });

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(softwareSchema) }} />

      <div className="min-h-screen bg-gradient-to-br from-white via-sky-50 to-sky-100 dark:from-gray-950 dark:to-gray-900">
        <div className="container mx-auto px-4 py-20">
          {/* Hero */}
          <div className="text-center mb-20">
            <div className="mx-auto mb-8">
              <Image src={LOGO_PATH} alt="Rayuela.ai logo" width={160} height={160} className="mx-auto" />
              <span className="block text-sm tracking-widest text-gray-500 dark:text-gray-400 uppercase mt-2">Recomendaciones API-first</span>
            </div>
            <h1 className="text-5xl md:text-6xl font-extrabold text-gray-900 dark:text-white leading-tight mb-4">
              IA que Conecta
            </h1>
            <div className="mx-auto mb-8">
              <Image
                src={CONNECTION_PATH}
                alt="Conexión entre productos y personas mediante IA"
                width={450}
                height={150}
              />
            </div>
            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-10">
              Personalizá tu marketplace en minutos con recomendaciones inteligentes, sin fricción técnica. Fácil de probar. Escalable por diseño.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button asChild size="lg" className="text-lg px-8 py-3">
                <Link href="/register">Accedé al Developer Sandbox</Link>
              </Button>
              <Button variant="outline" asChild size="lg" className="text-lg px-8 py-3">
                <Link href="/contact-sales">Solicitar Demo Personalizada</Link>
              </Button>
            </div>
          </div>

          {/* Developer Sandbox Info */}
          <div className="bg-orange-50 dark:bg-orange-900/10 border border-orange-300 dark:border-orange-700 rounded-lg p-4 mb-20 max-w-2xl mx-auto text-center text-sm text-orange-800 dark:text-orange-200">
            🧪 <strong>Sandbox Gratis:</strong> Datos de prueba incluidos. Sin riesgo. Ideal para testing y desarrollo.
          </div>

          {/* Value Propositions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-24">
            {[
              {
                emoji: '🤖',
                title: 'Recomendaciones con IA real',
                text: 'Híbrido, Learning-to-Rank y filtrado colaborativo de alto rendimiento, adaptado a tu negocio.',
              },
              {
                emoji: '⚙️',
                title: 'Integración API sin fricción',
                text: 'Diseñada por devs para devs. SDKs, Postman, entornos de prueba. Plug & play.',
              },
              {
                emoji: '🚀',
                title: 'Escalable y listo para producción',
                text: 'Infra multi-tenant, monitoreo, seguridad y métricas. Crece con tu operación.',
              },
            ].map((item, idx) => (
              <div
                key={idx}
                className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg text-center hover:scale-[1.015] transition"
              >
                <div className="w-16 h-16 bg-sky-100 dark:bg-sky-900 rounded-full flex items-center justify-center mx-auto mb-4 text-2xl">
                  {item.emoji}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">{item.title}</h3>
                <p className="text-gray-600 dark:text-gray-300">{item.text}</p>
              </div>
            ))}
          </div>

          {/* Final CTA */}
          <div className="text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Listo para dar el salto a la personalización inteligente?
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
              Probalo gratis o conectá con nuestro equipo. Estamos listos para ayudarte a escalar.
            </p>
            <Button asChild size="lg" className="text-lg px-8 py-3">
              <Link href="/register">Comenzar ahora</Link>
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
