#!/bin/bash

# Script para configurar URLs dinámicamente durante el despliegue
# Elimina la necesidad de URLs hardcodeadas en el código

set -e

PROJECT_ID="lateral-insight-461112-g9"
REGION="us-central1"

echo "🔧 === CONFIGURACIÓN DINÁMICA DE URLs ==="
echo ""

# Función para obtener URL de un servicio
get_service_url() {
    local service_name=$1
    local url=$(gcloud run services describe $service_name \
        --region=$REGION \
        --project=$PROJECT_ID \
        --format="value(status.url)" 2>/dev/null || echo "")
    echo $url
}

# Función para extraer hostname de una URL
get_hostname() {
    local url=$1
    echo $url | sed 's|https\?://||' | sed 's|/.*||'
}

echo "🔍 Detectando URLs de servicios desplegados..."

# Obtener URLs actuales
BACKEND_URL=$(get_service_url "rayuela-backend")
FRONTEND_URL=$(get_service_url "rayuela-frontend")

# Si no existen, intentar con nombres alternativos
if [ -z "$BACKEND_URL" ]; then
    BACKEND_URL=$(get_service_url "rayuela-backend-1002953244539")
fi

if [ -z "$FRONTEND_URL" ]; then
    FRONTEND_URL=$(get_service_url "rayuela-frontend-1002953244539")
fi

echo "📋 URLs detectadas:"
echo "   🔧 Backend: $BACKEND_URL"
echo "   🌐 Frontend: $FRONTEND_URL"

if [ -z "$BACKEND_URL" ] || [ -z "$FRONTEND_URL" ]; then
    echo "❌ Error: No se pudieron detectar las URLs de los servicios"
    echo "   Asegúrate de que los servicios estén desplegados"
    exit 1
fi

# Extraer hostnames para ALLOWED_HOSTS
BACKEND_HOST=$(get_hostname "$BACKEND_URL")
FRONTEND_HOST=$(get_hostname "$FRONTEND_URL")

echo ""
echo "🔄 Configurando variables de entorno..."

# Configurar backend
echo "   📡 Actualizando backend con CORS..."
gcloud run services update rayuela-backend \
    --region=$REGION \
    --project=$PROJECT_ID \
    --set-env-vars="FRONTEND_URL=$FRONTEND_URL,ALLOWED_HOSTS=$BACKEND_HOST" \
    --quiet

# Configurar frontend  
echo "   🌐 Actualizando frontend con API URL..."
gcloud run services update rayuela-frontend \
    --region=$REGION \
    --project=$PROJECT_ID \
    --set-env-vars="NEXT_PUBLIC_API_URL=$BACKEND_URL" \
    --quiet

echo ""
echo "✅ Configuración dinámica completada:"
echo "   🔧 Backend configurado para permitir: $FRONTEND_URL"
echo "   🌐 Frontend configurado para usar API: $BACKEND_URL"
echo "   🔒 CORS configurado correctamente"

echo ""
echo "🎉 ¡URLs configuradas dinámicamente sin hardcoding!"
