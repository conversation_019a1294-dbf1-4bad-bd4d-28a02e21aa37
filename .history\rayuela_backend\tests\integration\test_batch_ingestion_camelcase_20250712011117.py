"""
Integration test for batch ingestion with camelCase naming consistency.

This test validates that the BatchIngestionRequest schema correctly handles
camelCase data at all levels (top-level fields and nested schemas).
"""

import pytest
from fastapi.testclient import TestClient
from decimal import Decimal
import json

from src.main import app
from src.db.schemas.data_ingestion import BatchInges<PERSON>Request
from src.db.schemas.end_user import EndUserCreate
from src.db.schemas.product import ProductCreate
from src.db.schemas.interaction import InteractionCreate
from src.db.enums import InteractionType


class TestBatchIngestionCamelCase:
    """Test batch ingestion with consistent camelCase naming."""

    def test_batch_ingestion_request_schema_camelcase(self):
        """Test that BatchIngestionRequest accepts camelCase at all levels."""
        
        # Test data with camelCase naming at all levels
        camelcase_data = {
            "users": [
                {
                    "externalId": "user_001",
                    "preferredCategories": ["electronics", "books"],
                    "dislikedCategories": ["sports"],
                    "preferredBrands": ["Apple", "Samsung"],
                    "dislikedBrands": ["BrandX"],
                    "priceRangeMin": 10.50,
                    "priceRangeMax": 500.00,
                    "demographicInfo": {
                        "age": 28,
                        "gender": "M"
                    },
                    "onboardingPreferences": {
                        "shoppingFrequency": "weekly"
                    }
                }
            ],
            "products": [
                {
                    "externalId": "prod_001",
                    "name": "Smartphone XYZ",
                    "description": "A great smartphone",
                    "price": 599.99,
                    "category": "electronics",
                    "averageRating": 4.5,
                    "numRatings": 120,
                    "inventoryCount": 50
                }
            ],
            "interactions": [
                {
                    "userId": 1,
                    "productId": 1,
                    "interactionType": "VIEW",
                    "value": 1.0,
                    "recommendationMetadata": {
                        "source": "test"
                    }
                }
            ]
        }
        
        # This should not raise validation errors
        batch_request = BatchIngestionRequest(**camelcase_data)
        
        # Verify the data was parsed correctly
        assert batch_request.users is not None
        assert len(batch_request.users) == 1
        assert batch_request.users[0].external_id == "user_001"
        assert batch_request.users[0].preferred_categories == ["electronics", "books"]
        assert batch_request.users[0].price_range_min == Decimal("10.50")
        
        assert batch_request.products is not None
        assert len(batch_request.products) == 1
        assert batch_request.products[0].external_id == "prod_001"
        assert batch_request.products[0].average_rating == 4.5
        assert batch_request.products[0].num_ratings == 120
        
        assert batch_request.interactions is not None
        assert len(batch_request.interactions) == 1
        assert batch_request.interactions[0].user_id == 1
        assert batch_request.interactions[0].interaction_type == InteractionType.VIEW

    def test_batch_ingestion_request_schema_snake_case_compatibility(self):
        """Test that BatchIngestionRequest still accepts snake_case for backward compatibility."""
        
        # Test data with snake_case naming (should still work due to populate_by_name=True)
        snake_case_data = {
            "users": [
                {
                    "external_id": "user_001",
                    "preferred_categories": ["electronics", "books"],
                    "price_range_min": 10.50,
                    "demographic_info": {
                        "age": 28,
                        "gender": "M"
                    }
                }
            ],
            "products": [
                {
                    "external_id": "prod_001",
                    "name": "Smartphone XYZ",
                    "price": 599.99,
                    "category": "electronics",
                    "average_rating": 4.5,
                    "num_ratings": 120,
                    "inventory_count": 50
                }
            ],
            "interactions": [
                {
                    "user_id": 1,
                    "product_id": 1,
                    "interaction_type": "VIEW",
                    "value": 1.0,
                    "recommendation_metadata": {
                        "source": "test"
                    }
                }
            ]
        }
        
        # This should also not raise validation errors
        batch_request = BatchIngestionRequest(**snake_case_data)
        
        # Verify the data was parsed correctly
        assert batch_request.users[0].external_id == "user_001"
        assert batch_request.products[0].external_id == "prod_001"
        assert batch_request.interactions[0].user_id == 1

    def test_batch_ingestion_json_serialization_camelcase(self):
        """Test that BatchIngestionRequest serializes to camelCase JSON."""
        
        # Create a batch request with data
        batch_request = BatchIngestionRequest(
            users=[
                EndUserCreate(
                    external_id="user_001",
                    preferred_categories=["electronics"],
                    price_range_min=Decimal("10.50")
                )
            ],
            products=[
                ProductCreate(
                    external_id="prod_001",
                    name="Test Product",
                    price=Decimal("99.99"),
                    category="electronics",
                    average_rating=4.5,
                    num_ratings=10,
                    inventory_count=100
                )
            ],
            interactions=[
                InteractionCreate(
                    user_id=1,
                    product_id=1,
                    interaction_type=InteractionType.VIEW,
                    value=Decimal("1.0")
                )
            ]
        )
        
        # Serialize to JSON (should use camelCase aliases)
        json_data = batch_request.model_dump(by_alias=True)
        
        # Verify top-level fields are in camelCase
        assert "users" in json_data  # This field doesn't have underscores, so it stays the same
        assert "products" in json_data  # This field doesn't have underscores, so it stays the same
        assert "interactions" in json_data  # This field doesn't have underscores, so it stays the same
        
        # Verify nested fields are in camelCase
        user_data = json_data["users"][0]
        assert "externalId" in user_data
        assert "preferredCategories" in user_data
        assert "priceRangeMin" in user_data
        
        product_data = json_data["products"][0]
        assert "externalId" in product_data
        assert "averageRating" in product_data
        assert "numRatings" in product_data
        assert "inventoryCount" in product_data
        
        interaction_data = json_data["interactions"][0]
        assert "userId" in interaction_data
        assert "productId" in interaction_data
        assert "interactionType" in interaction_data

    def test_mixed_case_validation_error(self):
        """Test that mixing camelCase and snake_case in the same object raises appropriate errors."""
        
        # This should work - using consistent camelCase
        camelcase_data = {
            "users": [
                {
                    "externalId": "user_001",
                    "preferredCategories": ["electronics"]
                }
            ]
        }
        
        batch_request = BatchIngestionRequest(**camelcase_data)
        assert batch_request.users[0].external_id == "user_001"
        
        # This should also work - using consistent snake_case
        snake_case_data = {
            "users": [
                {
                    "external_id": "user_001", 
                    "preferred_categories": ["electronics"]
                }
            ]
        }
        
        batch_request = BatchIngestionRequest(**snake_case_data)
        assert batch_request.users[0].external_id == "user_001"
