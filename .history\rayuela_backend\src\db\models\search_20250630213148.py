from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    ForeignKey,
    Text,
    Index,
    func,
    ForeignKeyConstraint,
    PrimaryKeyConstraint,
    Identity,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from src.db.base import Base
from .mixins import TenantMixin, ACCOUNT_ID_FK, get_tenant_table_args


class Search(Base, TenantMixin):
    __tablename__ = "searches"

    search_id = Column(Integer, Identity(), primary_key=True)  # Match database schema
    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), nullable=False)
    end_user_id = Column(Integer, nullable=True)  # Match database schema
    query = Column(Text, nullable=False)  # Match database schema
    filters = Column(JSONB, nullable=True)  # Match database schema
    results_count = Column(Integer, nullable=True)  # Match database schema
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    session_id = Column(String(255), nullable=True)  # Match database schema
    created_at = Column(DateTime(timezone=True), server_default=func.now())  # Match database schema
    updated_at = Column(DateTime(timezone=True), server_default=func.now())  # Match database schema

    __table_args__ = get_tenant_table_args(
        # Simple FK to match database schema
        ForeignKeyConstraint(
            ["end_user_id"],
            ["end_users.end_user_id"],
            ondelete="SET NULL",
            name="searches_end_user_id_fkey"
        ),
        Index("idx_search_account_timestamp", "account_id", "timestamp"),
        Index("idx_search_end_user_timestamp", "end_user_id", "timestamp")
    )

    # Relationships
    account = relationship("Account", back_populates="searches")
    end_user = relationship(
        "EndUser",
        foreign_keys=[user_id],
        primaryjoin="and_(Search.account_id==EndUser.account_id, Search.user_id==EndUser.user_id)",
        back_populates="searches"
    )
