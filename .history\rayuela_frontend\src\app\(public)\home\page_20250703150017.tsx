// src/app/(public)/home/<USER>
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { generateMetadata as generateSEOMetadata, generateJsonLd } from '@/lib/seo';
import { IconText } from '@/components/ui/spacing-system';
// Importing directly from public via absolute path avoids webpack alias issues in production builds
const LOGO_PATH = '/logo-rayuela.png';
const CONNECTION_PATH = '/productos_personas.png';

export const metadata = generateSEOMetadata({
  title: 'Rayuela.ai: Transforma tu E-commerce en una Máquina de Personalización — Ventaja Competitiva Inmediata',
  description:
    'Mientras tus competidores muestran productos genéricos, tú entregas experiencias personalizadas que convierten. API de recomendaciones IA que posiciona tu plataforma como líder del mercado.',
  path: '/',
  keywords: [
    'ventaja competitiva e-commerce',
    'personalización empresarial',
    'API recomendaciones IA',
    'liderazgo tecnológico',
    'Rayuela.ai',
  ],
});

export default function HomePage() {
  const organizationSchema = generateJsonLd('Organization', {});
  const softwareSchema = generateJsonLd('SoftwareApplication', {
    name: 'Rayuela.ai',
    description:
      'Plataforma API-first de personalización inteligente para e-commerce y marketplaces',
  });

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(softwareSchema) }} />

      <div className="min-h-screen bg-gradient-to-br from-white via-sky-50 to-sky-100 dark:from-gray-950 dark:to-gray-900">
        <div className="container mx-auto px-4 py-20">
          {/* Hero */}
          <div className="text-center mb-20">
            <div className="mx-auto mb-8">
              <Image src={LOGO_PATH} alt="Rayuela.ai logo" width={160} height={160} className="mx-auto" />
              <span className="block text-sm tracking-widest text-gray-500 dark:text-gray-400 uppercase mt-2">Recomendaciones API-first</span>
            </div>
            <h1 className="text-display-2xl md:text-display-xl text-gray-900 dark:text-white leading-tight mb-4">
              Rayuela: Recomendaciones IA para Developers
            </h1>
            <div>
              <Image
                src={CONNECTION_PATH}
                alt="Conexión entre productos y personas mediante IA"
                width={450}
                height={150}
                className="mx-auto"
              />
            </div>
            <p className="text-body-large md:text-heading text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-10">
              Integra en minutos y escala sin límites. Personalización IA sin el dolor de cabeza del ML: tu equipo se enfoca en el core, nosotros en el valor.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button asChild size="lg" className="text-lg px-8 py-3">
                <Link href="/register">Accedé al Developer Sandbox</Link>
              </Button>
              <Button variant="outline" asChild size="lg" className="text-lg px-8 py-3">
                <Link href="/contact-sales">Solicitar Demo Personalizada</Link>
              </Button>
            </div>
          </div>

          {/* Developer Sandbox Info */}
          <div className="bg-orange-50 dark:bg-orange-900/10 border border-orange-300 dark:border-orange-700 rounded-lg p-4 mb-20 max-w-2xl mx-auto text-center text-sm text-orange-800 dark:text-orange-200">
            🧪 <strong>Sandbox Gratis:</strong> Datos de prueba incluidos. Sin riesgo. Ideal para testing y desarrollo.
          </div>

          {/* Value Propositions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-24">
            {[
              {
                emoji: '🤖',
                title: 'Recomendaciones con IA real',
                text: 'Híbrido, Learning-to-Rank y filtrado colaborativo de alto rendimiento, adaptado a tu negocio.',
              },
              {
                emoji: '⚙️',
                title: 'Integración API sin fricción',
                text: 'Diseñada por devs para devs. SDKs, Postman, entornos de prueba. Plug & play.',
              },
              {
                emoji: '🚀',
                title: 'Escalable y listo para producción',
                text: 'Infra multi-tenant, monitoreo, seguridad y métricas. Crece con tu operación.',
              },
            ].map((item, idx) => (
              <div
                key={idx}
                className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg text-center hover:scale-[1.015] transition"
              >
                <div className="w-16 h-16 bg-sky-100 dark:bg-sky-900 rounded-full flex items-center justify-center mx-auto mb-4 text-2xl">
                  {item.emoji}
                </div>
                <h3 className="text-heading text-gray-900 dark:text-white mb-2">{item.title}</h3>
                <p className="text-gray-600 dark:text-gray-300">{item.text}</p>
              </div>
            ))}
          </div>

          {/* Final CTA */}
          <div className="text-center">
            <h2 className="text-heading-large md:text-display text-gray-900 dark:text-white mb-4">
              Listo para dar el salto a la personalización inteligente?
            </h2>
            <p className="text-body-large text-gray-600 dark:text-gray-300 mb-6">
              Probalo gratis o conectá con nuestro equipo. Estamos listos para ayudarte a escalar.
            </p>
            <Button asChild size="lg" className="text-lg px-8 py-3">
              <Link href="/register">Comenzar ahora</Link>
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
